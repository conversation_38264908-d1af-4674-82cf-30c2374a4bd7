# -*- coding: utf-8 -*-
"""
主窗口模块 - 简化版本
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QTextEdit, QTabWidget, QFrame, QApplication,
    QSystemTrayIcon, QMenu, QProgressBar, QScrollArea, QGroupBox,
    QTableWidget, QHeaderView, QSplitter, QSizePolicy, QTableWidgetItem
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QIcon, QAction, QFont

from config.settings import Config
from api.client import APIClient
from utils.threads import WorkerThread
from .styles import StyleManager
from .dialogs import CustomMessageBox, ConfigDialog


class TeamManagerMainWindow(QMainWindow):
    """主窗口 - 简化版本"""

    def __init__(self):
        super().__init__()
        self.config = Config()
        self.api_client = APIClient(self.config)
        self.team_data = None
        self.credits_data = None
        self.worker_thread = None
        self.is_connected = False

        self.setWindowTitle("团队管理工具")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1000, 700)
        
        # 设置系统托盘
        self.setup_tray_icon()

        # 应用配置
        app = QApplication.instance()
        app.setStyleSheet(StyleManager.get_app_style())
        
        # 应用字体大小
        font_size = self.config.get('ui.font_size', 10)
        font = app.font()
        font.setPointSize(font_size)
        app.setFont(font)
        
        # 应用透明度
        opacity = self.config.get('ui.opacity', 100) / 100
        self.setWindowOpacity(opacity)

        # 启动时最小化
        if self.config.get('ui.start_minimized', False):
            self.showMinimized()

        # 创建加载覆盖层
        self.create_loading_overlay()
        
        # 初始化UI
        self.init_ui()
        self.init_menu()
        self.init_status_bar()

        # 设置刷新定时器
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.auto_refresh_data)
        self.update_refresh_timer()
        
    def setup_tray_icon(self):
        """设置系统托盘图标"""
        self.tray_icon = QSystemTrayIcon(self)
        
        try:
            icon = QIcon("team_manager_icon.png")
            if icon.isNull():
                icon = QIcon.fromTheme("applications-system")
            self.tray_icon.setIcon(icon)
        except Exception as e:
            print(f"无法加载托盘图标: {e}")
        
        # 创建托盘菜单
        tray_menu = QMenu()
        
        show_action = QAction("显示", self)
        show_action.triggered.connect(self.show_from_tray)
        tray_menu.addAction(show_action)
        
        hide_action = QAction("隐藏", self)
        hide_action.triggered.connect(self.hide)
        tray_menu.addAction(hide_action)
        
        tray_menu.addSeparator()
        
        refresh_action = QAction("刷新数据", self)
        refresh_action.triggered.connect(self.refresh_team_data)
        tray_menu.addAction(refresh_action)
        
        tray_menu.addSeparator()
        
        quit_action = QAction("退出", self)
        quit_action.triggered.connect(self.quit_from_tray)
        tray_menu.addAction(quit_action)
        
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.activated.connect(self.tray_icon_activated)
        self.tray_icon.show()
        
    def tray_icon_activated(self, reason):
        """处理托盘图标激活事件"""
        if reason == QSystemTrayIcon.ActivationReason.Trigger:
            if self.isHidden():
                self.show_from_tray()
            else:
                self.hide()
    
    def show_from_tray(self):
        """从托盘显示窗口"""
        self.show()
        self.activateWindow()
        
    def quit_from_tray(self):
        """从托盘退出程序"""
        QApplication.quit()

    def create_loading_overlay(self):
        """创建加载覆盖层"""
        self.loading_overlay = QWidget(self)
        self.loading_overlay.setGeometry(self.rect())
        self.loading_overlay.setStyleSheet("""
            QWidget {
                background-color: rgba(0, 0, 0, 0.4);
            }
        """)
        self.loading_overlay.hide()
        
        # 加载容器
        self.loading_container = QWidget(self.loading_overlay)
        self.loading_container.setStyleSheet("""
            QWidget {
                background-color: white;
                border-radius: 12px;
            }
        """)
        self.loading_container.setFixedSize(280, 160)
        
        # 加载布局
        loading_layout = QVBoxLayout(self.loading_container)
        loading_layout.setContentsMargins(20, 20, 20, 20)
        loading_layout.setSpacing(15)
        
        self.loading_label = QLabel("正在加载...")
        self.loading_label.setStyleSheet("""
            font-size: 16px;
            font-weight: 600;
            color: #4361ee;
        """)
        self.loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.loading_progress = QProgressBar()
        self.loading_progress.setRange(0, 0)
        self.loading_progress.setFixedHeight(6)
        
        self.loading_status = QLabel("正在连接服务器...")
        self.loading_status.setStyleSheet("""
            font-size: 13px;
            color: #64748b;
        """)
        self.loading_status.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        loading_layout.addWidget(self.loading_label)
        loading_layout.addWidget(self.loading_progress)
        loading_layout.addWidget(self.loading_status)
        loading_layout.addStretch()
        
        StyleManager.apply_shadow_effect(self.loading_container)

    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # 头部区域
        header = self.create_header_widget()
        main_layout.addWidget(header)

        # 标签页容器
        self.tab_widget = QTabWidget()
        self.tab_widget.setMinimumHeight(500)

        # 创建简化的标签页
        self.create_simple_tabs()

        main_layout.addWidget(self.tab_widget, 1)

        # 日志面板
        log_panel = self.create_log_widget()
        main_layout.addWidget(log_panel)

        central_widget.setLayout(main_layout)

    def create_header_widget(self):
        """创建美化的头部组件"""
        header = QFrame()
        header.setFixedHeight(120)
        header.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {StyleManager.PRIMARY_COLOR},
                    stop:0.5 {StyleManager.PRIMARY_LIGHT},
                    stop:1 {StyleManager.SECONDARY_COLOR});
                border-radius: 12px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }}
        """)

        layout = QHBoxLayout(header)
        layout.setContentsMargins(25, 15, 25, 15)
        layout.setSpacing(20)

        # 左侧 - 应用信息区域
        left_section = QWidget()
        left_layout = QHBoxLayout(left_section)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(15)

        # 动态logo
        logo_label = QLabel("🛠️")
        logo_label.setStyleSheet("""
            font-size: 36px;
            color: white;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 25px;
            padding: 10px;
            min-width: 50px;
            min-height: 50px;
        """)
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 应用信息
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(0, 5, 0, 5)
        info_layout.setSpacing(2)

        app_name = QLabel("团队管理工具")
        app_name.setStyleSheet("""
            font-size: 26px;
            font-weight: bold;
            color: white;
            margin: 0;
        """)

        app_subtitle = QLabel("Team Management Tool v1.0")
        app_subtitle.setStyleSheet("""
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
        """)

        info_layout.addWidget(app_name)
        info_layout.addWidget(app_subtitle)

        left_layout.addWidget(logo_label)
        left_layout.addWidget(info_widget)

        # 中间 - 统计概览
        stats_section = QWidget()
        stats_layout = QHBoxLayout(stats_section)
        stats_layout.setContentsMargins(0, 0, 0, 0)
        stats_layout.setSpacing(15)

        # 创建小型统计卡片
        self.header_members_stat = self.create_mini_stat_card("👥", "成员", "0")
        self.header_invites_stat = self.create_mini_stat_card("📧", "邀请", "0")
        self.header_credits_stat = self.create_mini_stat_card("💎", "积分", "0")

        stats_layout.addWidget(self.header_members_stat)
        stats_layout.addWidget(self.header_invites_stat)
        stats_layout.addWidget(self.header_credits_stat)

        # 右侧 - 状态和操作区域
        right_section = QWidget()
        right_layout = QVBoxLayout(right_section)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(8)

        # 连接状态
        self.connection_status = QLabel("🔴 未连接")
        self.connection_status.setStyleSheet("""
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            padding: 8px 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        """)
        self.connection_status.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 操作按钮
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(10)

        self.refresh_button = QPushButton("🔄 刷新")
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.25);
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 12px;
                color: white;
                padding: 8px 16px;
                font-size: 13px;
                font-weight: 600;
                min-width: 80px;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.35);
                border: 1px solid rgba(255, 255, 255, 0.6);
            }
            QPushButton:pressed {
                background: rgba(255, 255, 255, 0.15);
            }
        """)
        self.refresh_button.clicked.connect(self.refresh_team_data)

        self.config_button = QPushButton("⚙️ 设置")
        self.config_button.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.25);
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 12px;
                color: white;
                padding: 8px 16px;
                font-size: 13px;
                font-weight: 600;
                min-width: 80px;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.35);
                border: 1px solid rgba(255, 255, 255, 0.6);
            }
            QPushButton:pressed {
                background: rgba(255, 255, 255, 0.15);
            }
        """)
        self.config_button.clicked.connect(self.open_config_dialog)

        button_layout.addWidget(self.refresh_button)
        button_layout.addWidget(self.config_button)

        right_layout.addWidget(self.connection_status)
        right_layout.addWidget(button_container)

        # 添加所有部分到主布局
        layout.addWidget(left_section)
        layout.addWidget(stats_section)
        layout.addStretch()
        layout.addWidget(right_section)

        # 添加更强的阴影效果
        StyleManager.apply_shadow_effect(header, blur_radius=15, offset=(0, 5))
        return header

    def create_mini_stat_card(self, icon, title, value):
        """创建小型统计卡片"""
        card = QWidget()
        card.setFixedSize(80, 60)
        card.setStyleSheet("""
            QWidget {
                background: rgba(255, 255, 255, 0.15);
                border: 1px solid rgba(255, 255, 255, 0.25);
                border-radius: 10px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(2)

        # 图标和数值在同一行
        top_layout = QHBoxLayout()
        top_layout.setContentsMargins(0, 0, 0, 0)
        top_layout.setSpacing(3)

        icon_label = QLabel(icon)
        icon_label.setStyleSheet("""
            font-size: 16px;
            color: white;
        """)

        value_label = QLabel(value)
        value_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: white;
        """)

        top_layout.addWidget(icon_label)
        top_layout.addWidget(value_label)
        top_layout.addStretch()

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 10px;
            color: rgba(255, 255, 255, 0.8);
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        layout.addLayout(top_layout)
        layout.addWidget(title_label)

        # 存储值标签以便更新
        card.value_label = value_label
        return card

    def create_simple_tabs(self):
        """创建完整功能的标签页"""
        # 邀请成员标签页
        invite_tab = self.create_invite_tab()
        self.tab_widget.addTab(invite_tab, "邀请成员")

        # 团队管理标签页
        manage_tab = self.create_manage_tab()
        self.tab_widget.addTab(manage_tab, "团队管理")

        # 批量操作标签页
        batch_tab = self.create_batch_tab()
        self.tab_widget.addTab(batch_tab, "批量操作")

        # 数据视图标签页
        data_tab = self.create_data_tab()
        self.tab_widget.addTab(data_tab, "数据视图")

    def create_log_widget(self):
        """创建日志组件"""
        log_panel = StyleManager.create_card(title="系统日志", icon="📋")
        
        content_widget = QWidget()
        log_layout = QVBoxLayout(content_widget)
        log_layout.setContentsMargins(0, 0, 0, 0)
        
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setMaximumHeight(150)
        self.log_display.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 12px;
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 8px;
            }
        """)
        
        log_layout.addWidget(self.log_display)
        log_panel.layout().addWidget(content_widget)
        
        # 初始化日志
        self.log_entries = []
        QTimer.singleShot(100, lambda: self.log_info("系统", "团队管理工具已启动。"))
        
        return log_panel

    def init_menu(self):
        """初始化菜单栏"""
        menubar = self.menuBar()
        
        # 工具菜单
        tools_menu = menubar.addMenu('工具')
        
        config_action = QAction('配置设置', self)
        config_action.triggered.connect(self.open_config_dialog)
        tools_menu.addAction(config_action)

    def init_status_bar(self):
        """初始化状态栏"""
        self.status_label = QLabel("就绪")
        self.statusBar().addWidget(self.status_label)

    def open_config_dialog(self):
        """打开配置对话框"""
        dialog = ConfigDialog(self.config, self)
        dialog.config_changed.connect(self.apply_config)
        dialog.show()

    def apply_config(self):
        """应用新的配置设置"""
        print("正在应用新的配置设置...")
        self.api_client = APIClient(self.config)
        self.update_refresh_timer()

    def update_refresh_timer(self):
        """更新自动刷新定时器"""
        if self.config.get('ui.auto_refresh', True):
            interval = self.config.get('ui.refresh_interval', 30) * 1000
            self.refresh_timer.start(interval)
        else:
            self.refresh_timer.stop()

    def auto_refresh_data(self):
        """自动刷新数据"""
        if self.team_data and not (self.worker_thread and self.worker_thread.isRunning()):
            self.load_team_data()

    def refresh_team_data(self):
        """刷新团队数据"""
        self.load_team_data()

    def load_team_data(self):
        """加载团队数据"""
        if self.worker_thread and self.worker_thread.isRunning():
            return
            
        self.show_loading("正在加载团队数据...", "连接到服务器...")
        self.worker_thread = WorkerThread(self.api_client, "get_team_data")
        self.worker_thread.finished.connect(self.on_team_data_loaded)
        self.worker_thread.start()

    def on_team_data_loaded(self, success, message, data):
        """团队数据加载完成"""
        self.hide_loading()

        if success:
            self.team_data = data
            self.is_connected = True
            self.connection_status.setText("🟢 已连接")
            self.log_success("数据加载", "团队数据加载成功")

            # 更新数据显示
            self.update_data_displays()
        else:
            self.is_connected = False
            self.connection_status.setText("🔴 未连接")
            self.log_error("数据加载", f"加载失败: {message}")

    def update_data_displays(self):
        """更新数据显示"""
        if not self.team_data:
            return

        # 更新统计卡片
        self.update_stats_cards()

        # 更新表格
        self.update_tables()

        # 更新原始数据视图
        if hasattr(self, 'raw_data_display'):
            import json
            formatted_data = json.dumps(self.team_data, indent=2, ensure_ascii=False)
            self.raw_data_display.setPlainText(formatted_data)

    def update_stats_cards(self):
        """更新统计卡片"""
        if not self.team_data:
            return

        try:
            # 成员统计
            members = self.team_data.get('members', [])
            invitations = self.team_data.get('invitations', [])

            total_members = len(members)
            active_members = len([m for m in members if m.get('status') == 'active'])
            pending_members = len(invitations)

            # 更新详细统计卡片
            if hasattr(self, 'total_members_card'):
                self.total_members_card.value_label.setText(str(total_members))
            if hasattr(self, 'active_members_card'):
                self.active_members_card.value_label.setText(str(active_members))
            if hasattr(self, 'pending_members_card'):
                self.pending_members_card.value_label.setText(str(pending_members))

            # 更新头部小型统计卡片
            if hasattr(self, 'header_members_stat'):
                self.header_members_stat.value_label.setText(str(total_members))
            if hasattr(self, 'header_invites_stat'):
                self.header_invites_stat.value_label.setText(str(pending_members))

            # 邀请统计
            total_invitations = len(invitations)
            from datetime import datetime
            today = datetime.now().strftime('%Y-%m-%d')
            recent_invitations = len([i for i in invitations if i.get('createdAt', '').startswith(today)])

            if hasattr(self, 'total_invitations_card'):
                self.total_invitations_card.value_label.setText(str(total_invitations))
            if hasattr(self, 'recent_invitations_card'):
                self.recent_invitations_card.value_label.setText(str(recent_invitations))

            # 积分统计（如果有积分数据）
            credits = self.team_data.get('credits', {})
            available = credits.get('available', 0)
            used = credits.get('used', 0)
            pending_credits = credits.get('pending', 0)

            if hasattr(self, 'available_credits_card'):
                self.available_credits_card.value_label.setText(str(available))
            if hasattr(self, 'used_credits_card'):
                self.used_credits_card.value_label.setText(str(used))
            if hasattr(self, 'pending_credits_card'):
                self.pending_credits_card.value_label.setText(str(pending_credits))

            # 更新头部积分统计
            if hasattr(self, 'header_credits_stat'):
                self.header_credits_stat.value_label.setText(str(available))

        except Exception as e:
            self.log_error("统计更新", f"更新统计卡片失败: {str(e)}")

    def update_tables(self):
        """更新表格数据"""
        if not self.team_data:
            return

        try:
            # 更新成员表格
            if hasattr(self, 'members_table'):
                members = self.team_data.get('members', [])
                self.members_table.setRowCount(len(members))

                for row, member in enumerate(members):
                    self.members_table.setItem(row, 0, QTableWidgetItem(str(row + 1)))
                    self.members_table.setItem(row, 1, QTableWidgetItem(member.get('id', '')))
                    self.members_table.setItem(row, 2, QTableWidgetItem(member.get('email', '')))
                    self.members_table.setItem(row, 3, QTableWidgetItem(member.get('role', '')))
                    self.members_table.setItem(row, 4, QTableWidgetItem(member.get('joinedAt', '')))

            # 更新邀请表格
            if hasattr(self, 'invitations_table'):
                invitations = self.team_data.get('invitations', [])
                self.invitations_table.setRowCount(len(invitations))

                for row, invitation in enumerate(invitations):
                    self.invitations_table.setItem(row, 0, QTableWidgetItem(str(row + 1)))
                    self.invitations_table.setItem(row, 1, QTableWidgetItem(invitation.get('id', '')))
                    self.invitations_table.setItem(row, 2, QTableWidgetItem(invitation.get('email', '')))
                    self.invitations_table.setItem(row, 3, QTableWidgetItem(invitation.get('createdAt', '')))

        except Exception as e:
            self.log_error("表格更新", f"更新表格数据失败: {str(e)}")

    def show_loading(self, message="正在加载数据...", status=""):
        """显示加载覆盖层"""
        self.loading_label.setText(message)
        if status:
            self.loading_status.setText(status)
        self.loading_overlay.show()
        self.loading_container.move(
            (self.width() - self.loading_container.width()) // 2,
            (self.height() - self.loading_container.height()) // 2
        )
        
    def hide_loading(self):
        """隐藏加载覆盖层"""
        self.loading_overlay.hide()

    def log_info(self, title, message):
        """记录信息日志"""
        self._add_log_entry("信息", title, message, "#17a2b8")

    def log_success(self, title, message):
        """记录成功日志"""
        self._add_log_entry("成功", title, message, "#28a745")

    def log_error(self, title, message):
        """记录错误日志"""
        self._add_log_entry("错误", title, message, "#dc3545")

    def log_warning(self, title, message):
        """记录警告日志"""
        self._add_log_entry("警告", title, message, "#f97316")

    def _add_log_entry(self, level, title, message, color):
        """添加日志条目"""
        from datetime import datetime
        timestamp = datetime.now().strftime('%H:%M:%S')
        
        log_entry = {
            'timestamp': timestamp,
            'level': level,
            'title': title,
            'message': message,
            'color': color
        }
        
        self.log_entries.append(log_entry)
        
        # 限制日志条目数量
        if len(self.log_entries) > 100:
            self.log_entries = self.log_entries[-100:]
        
        # 更新显示
        self._update_log_display()
        
        # 输出到控制台
        print(f"[{timestamp}] {level}: {title} - {message}")

    def _update_log_display(self):
        """更新日志显示"""
        if not hasattr(self, 'log_display'):
            return
            
        html_content = ""
        for entry in self.log_entries[-10:]:  # 只显示最近10条
            html_content += f"""
            <div style="margin-bottom: 5px;">
                <span style="color: {entry['color']}; font-weight: bold;">
                    [{entry['timestamp']}] {entry['level']}:
                </span>
                <span style="color: #333;">
                    {entry['title']} - {entry['message']}
                </span>
            </div>
            """
        
        self.log_display.setHtml(html_content)
        
        # 滚动到底部
        cursor = self.log_display.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_display.setTextCursor(cursor)

    def resizeEvent(self, event):
        """处理窗口大小改变事件"""
        super().resizeEvent(event)
        if hasattr(self, 'loading_overlay'):
            self.loading_overlay.setGeometry(self.rect())
            self.loading_container.move(
                (self.width() - self.loading_container.width()) // 2,
                (self.height() - self.loading_container.height()) // 2
            )

    def create_invite_tab(self) -> QWidget:
        """创建邀请成员标签页"""
        # 主容器
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(20)

        # 信息卡片
        info_card = StyleManager.create_card(
            title="邀请团队成员",
            icon="✉️"
        )
        info_content = QLabel(
            "每行输入一个邮箱地址。"
            "系统将自动验证格式并发送邀请。"
        )
        info_content.setWordWrap(True)
        info_content.setStyleSheet("color: #64748b; line-height: 1.4;")
        info_card.layout().addWidget(info_content)
        layout.addWidget(info_card)

        # 邮箱输入卡片
        input_card = StyleManager.create_card(title="邮箱地址", icon="📧")

        # 邮箱输入字段
        self.email_input = QTextEdit()
        self.email_input.setPlaceholderText("<EMAIL>\<EMAIL>\<EMAIL>")
        self.email_input.setMinimumHeight(150)
        self.email_input.textChanged.connect(self.validate_emails_realtime)

        # 统计行
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(10)

        # 使用新设计的统计卡片
        self.total_emails_card = StyleManager.create_stat_card("📊", "总邮箱数", "0", StyleManager.PRIMARY_COLOR)
        self.valid_emails_card = StyleManager.create_stat_card("✅", "有效邮箱", "0", StyleManager.SUCCESS_COLOR)
        self.invalid_emails_card = StyleManager.create_stat_card("❌", "无效邮箱", "0", StyleManager.DANGER_COLOR)

        stats_layout.addWidget(self.total_emails_card)
        stats_layout.addWidget(self.valid_emails_card)
        stats_layout.addWidget(self.invalid_emails_card)
        stats_layout.addStretch()

        # 进度容器
        progress_container = QWidget()
        progress_layout = QVBoxLayout(progress_container)
        progress_layout.setContentsMargins(0, 10, 0, 0)
        progress_layout.setSpacing(5)

        progress_label = QLabel("验证进度:")

        self.validation_progress = QProgressBar()
        self.validation_progress.setValue(0)
        self.validation_progress.setMaximum(100)

        progress_layout.addWidget(progress_label)
        progress_layout.addWidget(self.validation_progress)

        # 按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        self.invite_btn = StyleManager.create_button("发送邀请", "success", "✉️")
        self.invite_btn.setMinimumWidth(150)
        self.invite_btn.clicked.connect(self.invite_members)
        self.invite_btn.setEnabled(False)

        self.clear_btn = StyleManager.create_button("清空输入", "danger", "🗑️")
        self.clear_btn.setMinimumWidth(150)
        self.clear_btn.clicked.connect(self.clear_email_input)

        self.paste_btn = StyleManager.create_button("从剪贴板粘贴", "primary", "📋")
        self.paste_btn.setMinimumWidth(150)
        self.paste_btn.clicked.connect(self.paste_clipboard)

        button_layout.addWidget(self.invite_btn)
        button_layout.addWidget(self.clear_btn)
        button_layout.addWidget(self.paste_btn)
        button_layout.addStretch()

        # Add all elements to input card
        input_content = QWidget()
        input_content_layout = QVBoxLayout(input_content)
        input_content_layout.setContentsMargins(0, 0, 0, 0)
        input_content_layout.setSpacing(15)

        input_content_layout.addWidget(self.email_input)
        input_content_layout.addLayout(stats_layout)
        input_content_layout.addWidget(progress_container)
        input_content_layout.addLayout(button_layout)

        input_card.layout().addWidget(input_content)
        layout.addWidget(input_card)

        # 历史记录卡片
        history_card = StyleManager.create_card(title="邀请历史", icon="📋")

        self.invite_history = QTextEdit()
        self.invite_history.setReadOnly(True)
        self.invite_history.setMinimumHeight(120)
        self.invite_history.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 12px;
                line-height: 1.4;
            }
        """)

        history_card.layout().addWidget(self.invite_history)
        layout.addWidget(history_card)

        # Add stretch to push everything to the top
        layout.addStretch()

        # Create a scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(container)

        # Final container
        final_container = QWidget()
        final_layout = QVBoxLayout(final_container)
        final_layout.setContentsMargins(0, 0, 0, 0)
        final_layout.addWidget(scroll_area)

        return final_container

    def validate_emails_realtime(self):
        """实时验证邮箱格式"""
        text = self.email_input.toPlainText().strip()
        if not text:
            self.total_emails_card.value_label.setText("0")
            self.valid_emails_card.value_label.setText("0")
            self.invalid_emails_card.value_label.setText("0")
            self.validation_progress.setValue(0)
            self.invite_btn.setEnabled(False)
            return

        emails = [email.strip() for email in text.split('\n') if email.strip()]
        total_count = len(emails)
        valid_count = 0
        invalid_count = 0

        for email in emails:
            if self.api_client.validate_email(email):
                valid_count += 1
            else:
                invalid_count += 1

        # 更新统计卡片
        self.total_emails_card.value_label.setText(str(total_count))
        self.valid_emails_card.value_label.setText(str(valid_count))
        self.invalid_emails_card.value_label.setText(str(invalid_count))

        # 更新进度条
        if total_count > 0:
            progress = int((valid_count / total_count) * 100)
            self.validation_progress.setValue(progress)
        else:
            self.validation_progress.setValue(0)

        # 启用/禁用邀请按钮
        self.invite_btn.setEnabled(valid_count > 0)

    def invite_members(self):
        """邀请成员"""
        text = self.email_input.toPlainText().strip()
        if not text:
            self.log_warning("邀请操作", "请输入邮箱地址")
            return

        emails = [email.strip() for email in text.split('\n') if email.strip()]
        valid_emails = [email for email in emails if self.api_client.validate_email(email)]

        if not valid_emails:
            self.log_error("邀请操作", "没有有效的邮箱地址")
            return

        if self.worker_thread and self.worker_thread.isRunning():
            self.log_warning("邀请操作", "正在处理其他请求，请稍候")
            return

        self.show_loading("正在发送邀请...", f"准备邀请 {len(valid_emails)} 个成员")
        self.worker_thread = WorkerThread(self.api_client, "invite_members", emails=valid_emails)
        self.worker_thread.finished.connect(self.on_invite_finished)
        self.worker_thread.start()

    def on_invite_finished(self, success, message, data):
        """邀请完成回调"""
        self.hide_loading()

        if success:
            self.log_success("邀请操作", message)
            # 添加到历史记录
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            history_text = f"[{timestamp}] 成功发送邀请\n"
            self.invite_history.append(history_text)
            # 清空输入
            self.email_input.clear()
        else:
            self.log_error("邀请操作", message)

    def clear_email_input(self):
        """清空邮箱输入"""
        self.email_input.clear()
        self.log_info("界面操作", "已清空邮箱输入框")

    def paste_clipboard(self):
        """从剪贴板粘贴内容到邮箱输入框"""
        clipboard = QApplication.clipboard()
        text = clipboard.text()
        if text:
            current = self.email_input.toPlainText()
            if current and not current.endswith('\n'):
                current += '\n'
            self.email_input.setPlainText(current + text)
            self.log_info("剪贴板操作", "已从剪贴板粘贴内容到邮箱输入框")
        else:
            self.log_warning("剪贴板操作", "剪贴板中没有文本内容")

    def create_manage_tab(self) -> QWidget:
        """创建团队管理标签页"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 创建内容widget
        content_widget = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # 控制按钮区域
        control_widget = QWidget()
        control_widget.setFixedHeight(60)
        control_layout = QHBoxLayout()
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(15)

        self.load_data_btn = StyleManager.create_button("🔄 获取团队数据", "primary")
        self.load_data_btn.clicked.connect(self.load_team_data)

        self.refresh_btn = StyleManager.create_button("🔃 刷新数据", "secondary")
        self.refresh_btn.clicked.connect(self.refresh_team_data)

        control_layout.addWidget(self.load_data_btn)
        control_layout.addWidget(self.refresh_btn)
        control_layout.addStretch()

        control_widget.setLayout(control_layout)
        layout.addWidget(control_widget)

        # 数据显示区域
        splitter = QSplitter(Qt.Orientation.Vertical)
        splitter.setHandleWidth(10)
        splitter.setStyleSheet("""
            QSplitter::handle {
                background: #dee2e6;
                border-radius: 5px;
                margin: 2px;
            }
            QSplitter::handle:hover {
                background: #007bff;
            }
        """)

        # 团队成员表格
        members_group = QGroupBox("👥 团队成员")
        members_layout = QVBoxLayout()
        members_layout.setSpacing(15)
        members_layout.setContentsMargins(25, 30, 25, 25)

        # 成员统计信息
        members_stats = self.create_members_stats_widget()
        members_layout.addWidget(members_stats)

        # 积分统计信息
        credits_stats = self.create_credits_stats_widget()
        members_layout.addWidget(credits_stats)

        # 创建表格容器
        table_container = QWidget()
        table_container.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 20px;
                border: 2px solid rgba(59, 130, 246, 0.1);
            }
        """)
        table_layout = QVBoxLayout(table_container)
        table_layout.setContentsMargins(2, 2, 2, 2)

        self.members_table = QTableWidget()
        self.members_table.setColumnCount(5)
        self.members_table.setHorizontalHeaderLabels(["序号", "ID", "邮箱", "角色", "加入时间"])
        self.members_table.setMinimumHeight(300)

        # 优化表格列宽设置
        header = self.members_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Interactive)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Interactive)

        # 设置列宽
        self.members_table.setColumnWidth(0, 80)
        self.members_table.setColumnWidth(1, 220)
        self.members_table.setColumnWidth(3, 120)
        self.members_table.setColumnWidth(4, 200)

        # 美化表格样式
        self.members_table.setStyleSheet(f"""
            QTableWidget {{
                background: {StyleManager.BACKGROUND_COLOR};
                border: none;
                border-radius: 12px;
                gridline-color: {StyleManager.NEUTRAL_MEDIUM};
                outline: none;
            }}
            QTableWidget::item {{
                padding: 12px;
                border-bottom: 1px solid {StyleManager.NEUTRAL_MEDIUM};
                font-size: 13px;
            }}
            QTableWidget::item:selected {{
                background: {StyleManager.PRIMARY_COLOR}15;
                color: {StyleManager.PRIMARY_COLOR};
                font-weight: 500;
            }}
            QHeaderView::section {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {StyleManager.PRIMARY_COLOR}, stop:1 {StyleManager.PRIMARY_LIGHT});
                color: white;
                font-weight: 500;
                font-size: 13px;
                padding: 12px;
                border: none;
            }}
            QHeaderView::section:first {{
                border-top-left-radius: 12px;
            }}
            QHeaderView::section:last {{
                border-top-right-radius: 12px;
            }}
        """)

        self.members_table.setAlternatingRowColors(True)
        self.members_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.members_table.verticalHeader().setVisible(False)
        self.members_table.setShowGrid(True)
        self.members_table.setFocusPolicy(Qt.FocusPolicy.NoFocus)
        self.members_table.verticalHeader().setDefaultSectionSize(50)

        table_layout.addWidget(self.members_table)
        members_layout.addWidget(table_container)

        # 添加表格容器阴影
        from PyQt6.QtWidgets import QGraphicsDropShadowEffect
        from PyQt6.QtGui import QColor
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(5)
        shadow.setColor(QColor(59, 130, 246, 30))
        table_container.setGraphicsEffect(shadow)

        members_group.setLayout(members_layout)
        StyleManager.apply_shadow_effect(members_group, blur_radius=8, offset=(0, 3))
        splitter.addWidget(members_group)

        # 邀请记录表格
        invitations_group = QGroupBox("📨 邀请记录")
        invitations_layout = QVBoxLayout()
        invitations_layout.setSpacing(15)
        invitations_layout.setContentsMargins(25, 30, 25, 25)

        # 邀请统计信息
        invitations_stats = self.create_invitations_stats_widget()
        invitations_layout.addWidget(invitations_stats)

        # 查询操作按钮区域
        query_control_layout = QHBoxLayout()
        query_control_layout.setContentsMargins(0, 5, 0, 15)
        query_control_layout.setSpacing(15)

        # 创建按钮容器
        button_container = QWidget()
        button_container.setStyleSheet("""
            QWidget {
                background: rgba(255, 255, 255, 0.8);
                border-radius: 20px;
                border: 2px solid rgba(59, 130, 246, 0.1);
            }
        """)
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(15, 10, 15, 10)
        button_layout.setSpacing(15)

        self.query_pending_emails_btn = StyleManager.create_button("🔍 查询未接受邮箱", "primary")
        self.query_pending_emails_btn.clicked.connect(self.query_pending_emails)
        self.query_pending_emails_btn.setToolTip("查询所有未接受邀请的邮箱地址")

        button_layout.addWidget(self.query_pending_emails_btn)
        button_layout.addStretch()

        query_control_layout.addWidget(button_container)
        invitations_layout.addLayout(query_control_layout)

        # 创建邀请表格容器
        inv_table_container = QWidget()
        inv_table_container.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 20px;
                border: 2px solid rgba(59, 130, 246, 0.1);
            }
        """)
        inv_table_layout = QVBoxLayout(inv_table_container)
        inv_table_layout.setContentsMargins(2, 2, 2, 2)

        self.invitations_table = QTableWidget()
        self.invitations_table.setColumnCount(4)
        self.invitations_table.setHorizontalHeaderLabels(["序号", "ID", "邮箱", "邀请时间"])
        self.invitations_table.setMinimumHeight(300)

        # 优化邀请表格列宽设置
        inv_header = self.invitations_table.horizontalHeader()
        inv_header.setStretchLastSection(True)
        inv_header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        inv_header.setSectionResizeMode(1, QHeaderView.ResizeMode.Interactive)
        inv_header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        inv_header.setSectionResizeMode(3, QHeaderView.ResizeMode.Interactive)

        # 设置列宽
        self.invitations_table.setColumnWidth(0, 80)
        self.invitations_table.setColumnWidth(1, 220)
        self.invitations_table.setColumnWidth(3, 200)

        # 美化邀请表格样式
        self.invitations_table.setStyleSheet(f"""
            QTableWidget {{
                background: {StyleManager.BACKGROUND_COLOR};
                border: none;
                border-radius: 12px;
                gridline-color: {StyleManager.NEUTRAL_MEDIUM};
                outline: none;
            }}
            QTableWidget::item {{
                padding: 12px;
                border-bottom: 1px solid {StyleManager.NEUTRAL_MEDIUM};
                font-size: 13px;
            }}
            QTableWidget::item:selected {{
                background: {StyleManager.PRIMARY_COLOR}15;
                color: {StyleManager.PRIMARY_COLOR};
                font-weight: 500;
            }}
            QHeaderView::section {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {StyleManager.PRIMARY_COLOR}, stop:1 {StyleManager.PRIMARY_LIGHT});
                color: white;
                font-weight: 500;
                font-size: 13px;
                padding: 12px;
                border: none;
            }}
            QHeaderView::section:first {{
                border-top-left-radius: 12px;
            }}
            QHeaderView::section:last {{
                border-top-right-radius: 12px;
            }}
        """)

        self.invitations_table.setAlternatingRowColors(True)
        self.invitations_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.invitations_table.verticalHeader().setVisible(False)
        self.invitations_table.setShowGrid(True)
        self.invitations_table.setFocusPolicy(Qt.FocusPolicy.NoFocus)
        self.invitations_table.verticalHeader().setDefaultSectionSize(50)

        inv_table_layout.addWidget(self.invitations_table)
        invitations_layout.addWidget(inv_table_container)

        # 添加邀请表格容器阴影
        inv_shadow = QGraphicsDropShadowEffect()
        inv_shadow.setBlurRadius(20)
        inv_shadow.setXOffset(0)
        inv_shadow.setYOffset(5)
        inv_shadow.setColor(QColor(59, 130, 246, 30))
        inv_table_container.setGraphicsEffect(inv_shadow)

        invitations_group.setLayout(invitations_layout)
        StyleManager.apply_shadow_effect(invitations_group, blur_radius=8, offset=(0, 3))
        splitter.addWidget(invitations_group)

        # 设置分割器比例
        splitter.setSizes([500, 400])

        layout.addWidget(splitter)
        layout.addStretch()
        content_widget.setLayout(layout)

        # 设置滚动区域的内容
        scroll_area.setWidget(content_widget)

        # 创建主容器
        main_widget = QWidget()
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)
        main_widget.setLayout(main_layout)

        return main_widget

    def create_members_stats_widget(self):
        """创建成员统计组件"""
        stats_widget = QWidget()
        stats_layout = QHBoxLayout()
        stats_layout.setContentsMargins(0, 0, 0, 0)
        stats_layout.setSpacing(10)

        # 使用统计卡片
        self.total_members_card = StyleManager.create_stat_card("👥", "总成员", "0", "#4361ee")
        self.active_members_card = StyleManager.create_stat_card("✅", "已加入", "0", "#22c55e")
        self.pending_members_card = StyleManager.create_stat_card("⏳", "待加入", "0", "#f97316")

        # 设置每个卡片的宽度比例
        self.total_members_card.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.active_members_card.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.pending_members_card.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)

        stats_layout.addWidget(self.total_members_card)
        stats_layout.addWidget(self.active_members_card)
        stats_layout.addWidget(self.pending_members_card)
        stats_layout.addStretch()

        stats_widget.setLayout(stats_layout)
        return stats_widget

    def create_credits_stats_widget(self):
        """创建积分统计组件"""
        stats_widget = QWidget()
        stats_layout = QHBoxLayout()
        stats_layout.setContentsMargins(0, 0, 0, 0)
        stats_layout.setSpacing(10)

        # 积分相关的统计卡片
        self.available_credits_card = StyleManager.create_stat_card("💎", "可用积分", "0", "#6366f1")
        self.used_credits_card = StyleManager.create_stat_card("📊", "已用积分", "0", "#ef4444")
        self.pending_credits_card = StyleManager.create_stat_card("⏱️", "待处理", "0", "#f59e0b")

        # 设置每个卡片的宽度比例
        self.available_credits_card.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.used_credits_card.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.pending_credits_card.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)

        stats_layout.addWidget(self.available_credits_card)
        stats_layout.addWidget(self.used_credits_card)
        stats_layout.addWidget(self.pending_credits_card)
        stats_layout.addStretch()

        stats_widget.setLayout(stats_layout)
        return stats_widget

    def create_invitations_stats_widget(self):
        """创建邀请统计组件"""
        stats_widget = QWidget()
        stats_layout = QHBoxLayout()
        stats_layout.setContentsMargins(0, 0, 0, 0)
        stats_layout.setSpacing(10)

        # 邀请相关的统计卡片
        self.total_invitations_card = StyleManager.create_stat_card("📨", "总邀请", "0", "#4361ee")
        self.recent_invitations_card = StyleManager.create_stat_card("🕒", "今日邀请", "0", "#f97316")

        # 设置每个卡片的宽度比例
        self.total_invitations_card.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.recent_invitations_card.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)

        stats_layout.addWidget(self.total_invitations_card)
        stats_layout.addWidget(self.recent_invitations_card)
        stats_layout.addStretch()

        stats_widget.setLayout(stats_layout)
        return stats_widget

    def query_pending_emails(self):
        """查询未接受邮箱"""
        if not self.team_data:
            self.log_warning("查询操作", "请先获取团队数据")
            return

        # 从团队数据中提取未接受的邮箱
        pending_emails = []
        try:
            if 'invitations' in self.team_data:
                for invitation in self.team_data['invitations']:
                    if 'email' in invitation:
                        pending_emails.append(invitation['email'])

            if pending_emails:
                from .dialogs import PendingEmailsDialog
                dialog = PendingEmailsDialog(pending_emails, self)
                dialog.show()
                self.log_success("查询操作", f"找到 {len(pending_emails)} 个未接受邀请的邮箱")
            else:
                self.log_info("查询操作", "没有找到未接受邀请的邮箱")
        except Exception as e:
            self.log_error("查询操作", f"查询失败: {str(e)}")

    def create_batch_tab(self) -> QWidget:
        """创建批量操作标签页"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 创建内容widget
        content_widget = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # 警告区域
        warning_widget = self.create_warning_widget()
        warning_widget.setFixedHeight(100)
        layout.addWidget(warning_widget)

        # 批量删除区域
        batch_group = QGroupBox("🔄 批量操作")
        batch_group.setMinimumHeight(280)
        batch_layout = QVBoxLayout()
        batch_layout.setSpacing(20)

        # 操作按钮网格
        from PyQt6.QtWidgets import QGridLayout
        button_grid = QGridLayout()
        button_grid.setSpacing(15)

        # 创建批量操作按钮
        self.batch_delete_unjoined_btn = self.create_batch_button(
            "🗑️ 删除未加入成员",
            "删除所有未加入团队的成员",
            [StyleManager.WARNING_COLOR, StyleManager.WARNING_LIGHT],
            self.batch_delete_unjoined_members
        )

        self.batch_delete_invitations_btn = self.create_batch_button(
            "📧 删除邀请记录",
            "删除所有待处理的邀请记录",
            [StyleManager.PRIMARY_COLOR, StyleManager.PRIMARY_LIGHT],
            self.batch_delete_invitations
        )

        self.batch_delete_all_btn = self.create_batch_button(
            "🚫 删除所有未确认",
            "删除所有未加入成员和邀请记录",
            [StyleManager.DANGER_COLOR, StyleManager.DANGER_LIGHT],
            self.batch_delete_all_unconfirmed
        )

        self.switch_to_community_plan_btn = self.create_batch_button(
            "🔄 切换到社区计划",
            "将当前登录账号切换到社区计划",
            [StyleManager.SUCCESS_COLOR, StyleManager.SUCCESS_LIGHT],
            self.switch_to_community_plan
        )

        self.switch_to_max_plan_btn = self.create_batch_button(
            "⭐ 切换到 Max 计划",
            "将当前登录账号切换到 Max 计划",
            [StyleManager.INFO_COLOR, StyleManager.INFO_LIGHT],
            self.switch_to_max_plan
        )

        # 积分加载按钮
        self.load_credits_btn = self.create_batch_button(
            "💎 加载积分数据",
            "获取当前账号的积分使用情况",
            [StyleManager.SECONDARY_COLOR, StyleManager.SECONDARY_LIGHT],
            self.load_credits_data
        )

        button_grid.addWidget(self.batch_delete_unjoined_btn, 0, 0)
        button_grid.addWidget(self.batch_delete_invitations_btn, 0, 1)
        button_grid.addWidget(self.batch_delete_all_btn, 1, 0)
        button_grid.addWidget(self.switch_to_community_plan_btn, 1, 1)
        button_grid.addWidget(self.switch_to_max_plan_btn, 2, 0)
        button_grid.addWidget(self.load_credits_btn, 2, 1)

        batch_layout.addLayout(button_grid)

        batch_group.setLayout(batch_layout)
        StyleManager.apply_shadow_effect(batch_group, blur_radius=8, offset=(0, 3))
        layout.addWidget(batch_group)

        # 操作日志区域
        log_group = QGroupBox("📋 操作日志")
        log_group.setMinimumHeight(280)
        log_layout = QVBoxLayout()

        # 日志控制按钮
        log_control_layout = QHBoxLayout()

        clear_log_btn = QPushButton("🧹 清空日志")
        clear_log_btn.clicked.connect(lambda: self.batch_log.clear())
        clear_log_btn.setStyleSheet("""
            QPushButton {
                background: #6c757d;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: #5a6268;
            }
        """)

        log_control_layout.addWidget(clear_log_btn)
        log_control_layout.addStretch()

        log_layout.addLayout(log_control_layout)

        self.batch_log = QTextEdit()
        self.batch_log.setReadOnly(True)
        self.batch_log.setFixedHeight(180)
        self.batch_log.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 2px solid rgba(79, 172, 254, 0.2);
                border-radius: 8px;
                padding: 8px;
                color: #495057;
            }
        """)
        log_layout.addWidget(self.batch_log)

        log_group.setLayout(log_layout)
        StyleManager.apply_shadow_effect(log_group, blur_radius=8, offset=(0, 3))
        layout.addWidget(log_group)

        # 添加弹性空间
        layout.addStretch()
        content_widget.setLayout(layout)

        # 设置滚动区域的内容
        scroll_area.setWidget(content_widget)

        # 创建主容器
        main_widget = QWidget()
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)
        main_widget.setLayout(main_layout)

        return main_widget

    def create_warning_widget(self):
        """创建警告组件"""
        warning_widget = QWidget()
        warning_widget.setStyleSheet(f"""
            QWidget {{
                background: {StyleManager.WARNING_LIGHT}30;
                border: 1px solid {StyleManager.WARNING_COLOR}40;
                border-radius: 8px;
                border-left: 5px solid {StyleManager.WARNING_COLOR};
            }}
        """)

        layout = QHBoxLayout()
        layout.setContentsMargins(20, 15, 20, 15)

        # 警告图标
        icon_label = QLabel("⚠️")
        icon_label.setStyleSheet("font-size: 24px;")

        # 警告文本
        warning_text = QLabel(
            "<b>重要提醒：</b><br>"
            "• 批量操作不可撤销，请谨慎使用<br>"
            "• 建议在操作前先备份重要数据<br>"
            "• 确保您有足够的权限执行这些操作"
        )
        warning_text.setStyleSheet(f"""
            QLabel {{
                color: {StyleManager.WARNING_DARK};
                font-size: 14px;
                background: transparent;
                border: none;
            }}
        """)

        layout.addWidget(icon_label)
        layout.addWidget(warning_text)
        layout.addStretch()

        warning_widget.setLayout(layout)
        StyleManager.apply_shadow_effect(warning_widget, blur_radius=5, offset=(0, 2))

        return warning_widget

    def create_batch_button(self, title, description, colors, callback=None):
        """创建批量操作按钮"""
        button_widget = QWidget()
        button_widget.setFixedHeight(100)
        button_widget.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 {colors[0]}, stop: 1 {colors[1]});
                border-radius: 8px;
                color: white;
            }}
            QWidget:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 {colors[1]}, stop: 1 {colors[0]});
            }}
        """)

        layout = QVBoxLayout()
        layout.setContentsMargins(20, 15, 20, 15)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: white;
                background: transparent;
            }
        """)

        # 描述
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.8);
                background: transparent;
            }
        """)
        desc_label.setWordWrap(True)

        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        layout.addStretch()

        button_widget.setLayout(layout)
        StyleManager.apply_shadow_effect(button_widget, blur_radius=5, offset=(0, 2))

        # 使按钮可点击
        if callback:
            button_widget.mousePressEvent = lambda event: callback() if event.button() == Qt.MouseButton.LeftButton else None
            button_widget.setCursor(Qt.CursorShape.PointingHandCursor)

        return button_widget

    def batch_delete_unjoined_members(self):
        """批量删除未加入成员"""
        if not self.team_data:
            self.log_warning("操作失败", "请先加载团队数据")
            return

        unjoined_ids = self.get_unjoined_member_ids()
        if not unjoined_ids:
            self.log_info("批量删除", "没有找到未加入的成员")
            return

        # 确认对话框
        from .dialogs import CustomConfirmDialog
        if not CustomConfirmDialog.ask_confirmation(
            self, "确认批量删除",
            f"准备删除 {len(unjoined_ids)} 个未加入成员，此操作不可撤销！\n\n确定要继续吗？"
        ):
            return

        self.log_warning("批量删除确认", f"准备删除 {len(unjoined_ids)} 个未加入成员，此操作不可撤销！")
        self.log_batch_operation(f"开始批量删除 {len(unjoined_ids)} 个未加入成员")
        self.start_worker_thread("batch_delete", member_ids=unjoined_ids)

    def batch_delete_invitations(self):
        """批量删除邀请记录"""
        if not self.team_data:
            self.log_warning("操作失败", "请先加载团队数据")
            return

        invitation_ids = self.get_invitation_ids()
        if not invitation_ids:
            self.log_info("批量删除", "没有找到邀请记录")
            return

        # 确认对话框
        from .dialogs import CustomConfirmDialog
        if not CustomConfirmDialog.ask_confirmation(
            self, "确认批量删除",
            f"准备删除 {len(invitation_ids)} 条邀请记录，此操作不可撤销！\n\n确定要继续吗？"
        ):
            return

        self.log_warning("批量删除确认", f"准备删除 {len(invitation_ids)} 条邀请记录，此操作不可撤销！")
        self.log_batch_operation(f"开始批量删除 {len(invitation_ids)} 条邀请记录")
        self.start_worker_thread("batch_delete", member_ids=invitation_ids)

    def batch_delete_all_unconfirmed(self):
        """批量删除所有未确认的成员和邀请"""
        if not self.team_data:
            self.log_warning("操作失败", "请先加载团队数据")
            return

        unjoined_ids = self.get_unjoined_member_ids()
        invitation_ids = self.get_invitation_ids()
        all_ids = unjoined_ids + invitation_ids

        if not all_ids:
            self.log_info("批量删除", "没有找到未确认的成员或邀请")
            return

        # 确认对话框
        from .dialogs import CustomConfirmDialog
        if not CustomConfirmDialog.ask_confirmation(
            self, "确认批量删除",
            f"准备删除所有未确认的成员和邀请：\n"
            f"• 未加入成员: {len(unjoined_ids)} 个\n"
            f"• 邀请记录: {len(invitation_ids)} 条\n"
            f"• 总计: {len(all_ids)} 项\n\n"
            f"此操作不可撤销！确定要继续吗？"
        ):
            return

        self.log_warning("批量删除确认",
                        f"准备删除所有未确认的成员和邀请 - "
                        f"未加入成员: {len(unjoined_ids)} 个, "
                        f"邀请记录: {len(invitation_ids)} 条, "
                        f"总计: {len(all_ids)} 项，此操作不可撤销！")
        self.log_batch_operation(f"开始批量删除所有未确认项目，共 {len(all_ids)} 项")
        self.start_worker_thread("batch_delete", member_ids=all_ids)

    def load_credits_data(self):
        """加载积分数据"""
        self.start_worker_thread("get_credits")

    def get_unjoined_member_ids(self):
        """获取未加入成员的ID列表"""
        if not self.team_data:
            return []

        ids = []
        users = self.extract_users_from_data(self.team_data)
        for user in users:
            if not user.get('role'):  # 没有角色表示未加入
                ids.append(user.get('id', ''))

        return [id for id in ids if id]

    def get_invitation_ids(self):
        """获取邀请记录的ID列表"""
        if not self.team_data:
            return []

        ids = []
        invitations = self.extract_invitations_from_data(self.team_data)
        for invitation in invitations:
            ids.append(invitation.get('id', ''))

        return [id for id in ids if id]

    def extract_users_from_data(self, data):
        """从数据中提取用户列表"""
        users = []
        if isinstance(data, dict):
            for key, value in data.items():
                # 支持多种字段名：users, members
                if key in ["users", "members"] and isinstance(value, list):
                    users.extend(value)
                elif isinstance(value, (dict, list)):
                    users.extend(self.extract_users_from_data(value))
        elif isinstance(data, list):
            for item in data:
                users.extend(self.extract_users_from_data(item))
        return users

    def extract_invitations_from_data(self, data):
        """从数据中提取邀请列表"""
        invitations = []
        if isinstance(data, dict):
            for key, value in data.items():
                # 支持多种字段名：invitations, invites
                if key in ["invitations", "invites"] and isinstance(value, list):
                    invitations.extend(value)
                elif isinstance(value, (dict, list)):
                    invitations.extend(self.extract_invitations_from_data(value))
        elif isinstance(data, list):
            for item in data:
                invitations.extend(self.extract_invitations_from_data(item))
        return invitations

    def create_data_tab(self) -> QWidget:
        """创建数据视图标签页 - 简化版本"""
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # 数据视图卡片
        data_card = StyleManager.create_card(title="原始数据视图", icon="📊")

        self.raw_data_display = QTextEdit()
        self.raw_data_display.setReadOnly(True)
        self.raw_data_display.setMinimumHeight(400)
        self.raw_data_display.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 11px;
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 8px;
            }
        """)

        data_card.layout().addWidget(self.raw_data_display)
        layout.addWidget(data_card)

        return container

    def switch_to_community_plan(self):
        """切换到 Community Plan"""
        if self.worker_thread and self.worker_thread.isRunning():
            self.log_warning("计划切换", "正在处理其他请求，请稍候")
            return

        self.show_loading("正在切换计划...", "切换到 Community Plan")
        self.worker_thread = WorkerThread(self.api_client, "put_user_on_community_plan")
        self.worker_thread.finished.connect(self.on_plan_switch_finished)
        self.worker_thread.start()

    def switch_to_max_plan(self):
        """切换到 Max Plan"""
        if self.worker_thread and self.worker_thread.isRunning():
            self.log_warning("计划切换", "正在处理其他请求，请稍候")
            return

        self.show_loading("正在切换计划...", "切换到 Max Plan")
        self.worker_thread = WorkerThread(self.api_client, "put_user_on_max_plan")
        self.worker_thread.finished.connect(self.on_plan_switch_finished)
        self.worker_thread.start()

    def on_plan_switch_finished(self, success, message, data):
        """计划切换完成回调"""
        self.hide_loading()

        if success:
            self.log_success("计划切换", message)
            # 添加到批量操作历史
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            history_text = f"[{timestamp}] {message}\n"
            self.batch_history.append(history_text)
        else:
            self.log_error("计划切换", message)

    def start_worker_thread(self, operation: str, **kwargs):
        """启动工作线程"""
        if self.worker_thread and self.worker_thread.isRunning():
            self.log_warning("操作冲突", "有操作正在进行中，请稍候...")
            return

        # 操作名称映射
        operation_names = {
            "get_team_data": "获取团队数据",
            "invite_members": "邀请成员",
            "batch_delete": "批量删除",
            "put_user_on_community_plan": "切换到社区计划",
            "put_user_on_max_plan": "切换到Max计划",
            "get_credits": "获取积分信息"
        }

        # 操作状态文本映射
        operation_status = {
            "get_team_data": "正在连接服务器获取团队数据...",
            "invite_members": "正在发送邀请请求...",
            "batch_delete": "正在执行批量删除操作，请稍候...",
            "put_user_on_community_plan": "正在切换账号计划类型...",
            "put_user_on_max_plan": "正在切换账号计划类型...",
            "get_credits": "正在获取积分使用情况..."
        }

        # 显示加载覆盖层
        op_name = operation_names.get(operation, operation)
        op_status = operation_status.get(operation, "正在处理请求...")
        self.show_loading(f"正在{op_name}", op_status)

        # 创建并配置工作线程
        self.worker_thread = WorkerThread(self.api_client, operation, **kwargs)
        self.worker_thread.finished.connect(self.on_worker_finished)
        self.worker_thread.progress.connect(self.on_worker_progress)

        # 更新状态栏信息
        self.status_label.setText(f"正在执行: {op_name}")

        # 记录操作开始
        self.log_info("操作开始", f"正在执行: {op_name}")

        # 启动线程
        self.worker_thread.start()

    def on_worker_finished(self, success: bool, message: str, data):
        """工作线程完成回调"""
        # 隐藏加载覆盖层
        self.hide_loading()

        # 重置状态栏
        self.status_label.setText("就绪")

        if success:
            if data is not None:  # 获取数据操作
                # 检查是否是积分数据
                if self.worker_thread.operation == "get_credits":
                    self.credits_data = data
                    self.log_info("积分更新", "正在处理和显示积分数据...")
                    try:
                        # 更新积分显示
                        self.update_credits_display(data)
                        # 记录成功日志
                        self.log_success("积分加载成功", f"积分数据已更新")
                    except Exception as e:
                        self.log_error("积分显示错误", f"显示积分数据时发生错误: {str(e)}")
                else:
                    # 团队数据处理
                    self.team_data = data
                    self.log_info("数据更新", "正在处理和显示团队数据...")
                    try:
                        # 更新界面显示
                        self.update_data_displays()
                        # 更新连接状态
                        self.is_connected = True
                        self.connection_status.setText("🟢 已连接")
                        # 记录成功日志
                        self.log_success("数据加载成功", "团队数据已更新")
                    except Exception as e:
                        self.log_error("数据显示错误", f"显示数据时发生错误: {str(e)}")
            else:  # 其他操作
                if "邀请" in message:
                    self.log_invite_history(message)
                    self.log_success("邀请成功", message)
                elif "删除" in message:
                    self.log_batch_operation(message)
                    self.log_success("操作完成", message)
                    # 自动刷新数据
                    if self.team_data:
                        self.load_team_data()
                else:
                    self.log_success("操作完成", message)
        else:
            self.is_connected = False
            self.connection_status.setText("🔴 未连接")
            self.log_error("操作失败", message)

    def on_worker_progress(self, progress: int, status: str):
        """工作线程进度回调"""
        self.loading_status.setText(status)

    def log_batch_operation(self, message: str):
        """记录批量操作日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {message}\n"
        if hasattr(self, 'batch_history'):
            self.batch_history.append(log_entry)

    def log_invite_history(self, message: str):
        """记录邀请历史"""
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {message}\n"
        if hasattr(self, 'invite_history'):
            self.invite_history.append(log_entry)

    def update_credits_display(self, credits_data):
        """更新积分数据显示"""
        if not credits_data:
            return

        try:
            # 提取积分数据
            available_credits = credits_data.get('usageUnitsAvailable', 0)
            used_credits = credits_data.get('usageUnitsUsedThisBillingCycle', 0)
            pending_credits = credits_data.get('usageUnitsPending', 0)

            # 更新积分统计卡片
            if hasattr(self, 'available_credits_card'):
                self.available_credits_card.value_label.setText(str(available_credits))
            if hasattr(self, 'used_credits_card'):
                self.used_credits_card.value_label.setText(str(used_credits))
            if hasattr(self, 'pending_credits_card'):
                self.pending_credits_card.value_label.setText(str(pending_credits))

            # 更新头部积分统计
            if hasattr(self, 'header_credits_stat'):
                self.header_credits_stat.value_label.setText(str(available_credits))

            # 记录积分更新成功
            self.log_info("积分更新", f"可用积分: {available_credits}, 已用积分: {used_credits}, 待处理: {pending_credits}")

        except Exception as e:
            self.log_error("积分更新", f"更新积分显示时发生错误: {e}")

    def closeEvent(self, event):
        """关闭事件处理"""
        if self.config.get('features.auto_save', True):
            self.config.save_config()

        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.terminate()
            self.worker_thread.wait()

        # 检查是否应该关闭到系统托盘
        if self.config.get('ui.close_to_tray', False) and self.tray_icon.isVisible():
            self.hide()
            event.ignore()
        else:
            event.accept()
