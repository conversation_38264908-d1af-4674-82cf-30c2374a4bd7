# -*- coding: utf-8 -*-
"""
UI对话框模块
"""

from PyQt6.QtWidgets import (
    QWidget, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTextEdit, QGroupBox, QFormLayout, QLineEdit, QComboBox,
    QSpinBox, QCheckBox, QTabWidget, QScrollArea, QFileDialog
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QColor
from datetime import datetime
import json
from typing import List

from .styles import StyleManager
from config.settings import Config


class CustomMessageBox(QDialog):
    """自定义消息框，确保内容完整显示"""

    def __init__(self, title, message, msg_type="info", parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowCloseButtonHint)
        self.setModal(True)
        self.init_ui(title, message, msg_type)

    def init_ui(self, title, message, msg_type):
        """初始化消息框界面"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 根据类型设置图标和颜色
        if msg_type == "success":
            icon = "✅"
            color = StyleManager.SUCCESS_COLOR
        elif msg_type == "error":
            icon = "❌"
            color = StyleManager.DANGER_COLOR
        elif msg_type == "warning":
            icon = "⚠️"
            color = StyleManager.WARNING_COLOR
        else:
            icon = "ℹ️"
            color = StyleManager.PRIMARY_COLOR

        # 标题区域
        title_layout = QHBoxLayout()
        title_icon = QLabel(icon)
        title_icon.setStyleSheet("font-size: 24px;")
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 16px;
                font-weight: bold;
                color: {color};
                margin-left: 10px;
            }}
        """)
        title_layout.addWidget(title_icon)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        layout.addLayout(title_layout)

        # 消息内容
        msg_label = QLabel(message)
        msg_label.setWordWrap(True)
        msg_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        msg_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                line-height: 1.5;
                padding: 10px;
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #dee2e6;
            }
        """)
        msg_label.setMinimumWidth(400)
        msg_label.setMaximumWidth(600)
        layout.addWidget(msg_label)

        # 按钮区域
        button_layout = QHBoxLayout()
        ok_btn = StyleManager.create_button("确定", "primary")
        ok_btn.clicked.connect(self.accept)
        button_layout.addStretch()
        button_layout.addWidget(ok_btn)
        layout.addLayout(button_layout)

        self.setLayout(layout)

        # 根据内容调整大小
        self.adjustSize()
        self.setMinimumSize(450, 200)

        # 应用样式和阴影
        self.setStyleSheet(f"""
            QWidget {{
                background: white;
                border-radius: 12px;
            }}
        """)
        StyleManager.apply_shadow_effect(self, blur_radius=20, offset=(0, 10))

    def accept(self):
        """确定按钮点击事件"""
        self.close()

    @staticmethod
    def show_info(parent, title, message):
        """显示信息消息框"""
        dialog = CustomMessageBox(title, message, "info", parent)
        dialog.show()
        return dialog

    @staticmethod
    def show_success(parent, title, message):
        """显示成功消息框"""
        dialog = CustomMessageBox(title, message, "success", parent)
        dialog.show()
        return dialog

    @staticmethod
    def show_warning(parent, title, message):
        """显示警告消息框"""
        dialog = CustomMessageBox(title, message, "warning", parent)
        dialog.show()
        return dialog

    @staticmethod
    def show_error(parent, title, message):
        """显示错误消息框"""
        dialog = CustomMessageBox(title, message, "error", parent)
        dialog.show()
        return dialog


class CustomConfirmDialog(QDialog):
    """自定义确认对话框"""

    def __init__(self, title, message, parent=None):
        super().__init__(parent)
        self.result = False
        self.setWindowTitle(title)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowCloseButtonHint)
        self.setModal(True)
        self.init_ui(title, message)

    def init_ui(self, title, message):
        """初始化确认对话框界面"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题区域
        title_layout = QHBoxLayout()
        title_icon = QLabel("❓")
        title_icon.setStyleSheet("font-size: 24px;")
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 16px;
                font-weight: bold;
                color: {StyleManager.WARNING_COLOR};
                margin-left: 10px;
            }}
        """)
        title_layout.addWidget(title_icon)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        layout.addLayout(title_layout)

        # 消息内容
        msg_label = QLabel(message)
        msg_label.setWordWrap(True)
        msg_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        msg_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                line-height: 1.5;
                padding: 10px;
                background: #fff3cd;
                border-radius: 8px;
                border: 1px solid #ffeaa7;
                color: #856404;
            }
        """)
        msg_label.setMinimumWidth(400)
        msg_label.setMaximumWidth(600)
        layout.addWidget(msg_label)

        # 按钮区域
        button_layout = QHBoxLayout()
        cancel_btn = StyleManager.create_button("取消", "")
        cancel_btn.clicked.connect(self.reject)

        confirm_btn = StyleManager.create_button("确定", "warning")
        confirm_btn.clicked.connect(self.accept)

        button_layout.addStretch()
        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(confirm_btn)
        layout.addLayout(button_layout)

        self.setLayout(layout)

        # 根据内容调整大小
        self.adjustSize()
        self.setMinimumSize(450, 200)

        # 应用样式和阴影
        self.setStyleSheet(f"""
            QWidget {{
                background: white;
                border-radius: 12px;
            }}
        """)
        StyleManager.apply_shadow_effect(self, blur_radius=20, offset=(0, 10))

    def accept(self):
        """确定按钮点击事件"""
        self.result = True
        self.close()

    def reject(self):
        """取消按钮点击事件"""
        self.result = False
        self.close()

    @staticmethod
    def ask_confirmation(parent, title, message):
        """显示确认对话框并返回结果"""
        dialog = CustomConfirmDialog(title, message, parent)
        dialog.exec()
        return dialog.result


class PendingEmailsDialog(QWidget):
    """未接受邮箱列表弹窗"""

    def __init__(self, emails: List[str], parent=None):
        super().__init__(parent)
        self.emails = emails
        self.setWindowTitle("📧 未接受邀请的邮箱列表")
        self.setGeometry(300, 200, 600, 500)
        self.setMinimumSize(500, 400)
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowCloseButtonHint)
        self.init_ui()

    def init_ui(self):
        """初始化弹窗界面"""
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # 标题区域
        title_layout = QHBoxLayout()
        title_icon = QLabel("📧")
        title_icon.setStyleSheet("font-size: 24px;")
        title_label = QLabel(f"未接受邀请的邮箱列表 ({len(self.emails)} 个)")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 18px;
                font-weight: bold;
                color: {StyleManager.PRIMARY_COLOR};
                margin-left: 10px;
            }}
        """)
        title_layout.addWidget(title_icon)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        main_layout.addLayout(title_layout)

        # 邮箱列表显示区域
        list_group = QGroupBox("邮箱地址")
        list_layout = QVBoxLayout()
        list_layout.setSpacing(10)

        # 创建文本编辑器显示邮箱列表
        self.email_text = QTextEdit()
        self.email_text.setPlainText('\n'.join(self.emails))
        self.email_text.setReadOnly(True)
        self.email_text.setMinimumHeight(250)
        self.email_text.setStyleSheet(f"""
            QTextEdit {{
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                line-height: 1.5;
                border: 2px solid {StyleManager.NEUTRAL_MEDIUM};
                border-radius: 8px;
                padding: 10px;
                background: {StyleManager.BACKGROUND_COLOR};
                selection-background-color: {StyleManager.PRIMARY_COLOR}40;
            }}
        """)
        list_layout.addWidget(self.email_text)

        # 统计信息
        stats_label = QLabel(f"总计: {len(self.emails)} 个邮箱地址")
        stats_label.setStyleSheet(f"""
            QLabel {{
                color: {StyleManager.NEUTRAL_DARK};
                font-size: 12px;
                padding: 5px;
                background: {StyleManager.NEUTRAL_LIGHT};
                border-radius: 4px;
            }}
        """)
        list_layout.addWidget(stats_label)

        list_group.setLayout(list_layout)
        main_layout.addWidget(list_group)

        # 操作按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        # 全选按钮
        select_all_btn = StyleManager.create_button("📋 全选", "primary")
        select_all_btn.clicked.connect(self.select_all_emails)
        select_all_btn.setToolTip("选择所有邮箱地址")

        # 复制按钮
        copy_btn = StyleManager.create_button("📄 复制到剪贴板", "success")
        copy_btn.clicked.connect(self.copy_to_clipboard)
        copy_btn.setToolTip("复制所有邮箱地址到剪贴板")

        # 导出按钮
        export_btn = StyleManager.create_button("💾 导出到文件", "info")
        export_btn.clicked.connect(self.export_to_file)
        export_btn.setToolTip("导出邮箱列表到文本文件")

        # 关闭按钮
        close_btn = StyleManager.create_button("❌ 关闭", "danger")
        close_btn.clicked.connect(self.close)

        button_layout.addWidget(select_all_btn)
        button_layout.addWidget(copy_btn)
        button_layout.addWidget(export_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)

        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)

        # 应用样式
        self.setStyleSheet(f"""
            QWidget {{
                background: {StyleManager.BACKGROUND_COLOR};
            }}
        """)

        # 添加阴影效果
        StyleManager.apply_shadow_effect(self, blur_radius=20, offset=(0, 10))

    def select_all_emails(self):
        """全选所有邮箱地址"""
        self.email_text.selectAll()

    def copy_to_clipboard(self):
        """复制邮箱列表到剪贴板"""
        from PyQt6.QtWidgets import QApplication
        clipboard = QApplication.clipboard()
        email_text = '\n'.join(self.emails)
        clipboard.setText(email_text)

        # 显示成功提示
        CustomMessageBox.show_success(self, "复制成功",
                              f"已复制 {len(self.emails)} 个邮箱地址到剪贴板\n\n"
                              "每行一个邮箱，方便粘贴使用。")

    def export_to_file(self):
        """导出邮箱列表到文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出邮箱列表",
            f"pending_emails_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "文本文件 (*.txt);;所有文件 (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(self.emails))
                CustomMessageBox.show_success(self, "导出成功",
                                      f"邮箱列表已成功导出到:\n{file_path}")
            except Exception as e:
                CustomMessageBox.show_error(self, "导出失败", f"导出文件时发生错误:\n{str(e)}")


# 配置对话框将在下一个文件中实现，因为它太大了
class ConfigDialog(QDialog):
    """配置对话框 - 简化版本"""

    config_changed = pyqtSignal()

    def __init__(self, config: Config, parent=None):
        super().__init__(parent)
        self.config = config
        self.setWindowTitle("🔧 配置设置")
        self.setGeometry(200, 200, 600, 400)
        self.setMinimumSize(500, 300)
        self.init_ui()
    
    def init_ui(self):
        """初始化简化的配置UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("🔧 配置设置")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 18px;
                font-weight: bold;
                color: {StyleManager.PRIMARY_COLOR};
                padding: 10px;
            }}
        """)
        layout.addWidget(title_label)

        # 基本配置组
        config_group = QGroupBox("基本配置")
        config_layout = QFormLayout()

        # API URL
        self.api_url_edit = QLineEdit(self.config.get('api.base_url', ''))
        config_layout.addRow("API URL:", self.api_url_edit)

        # Cookie
        self.cookie_edit = QTextEdit()
        self.cookie_edit.setPlainText(self.config.get('api.headers.cookie', ''))
        self.cookie_edit.setMaximumHeight(100)
        config_layout.addRow("Cookie:", self.cookie_edit)

        config_group.setLayout(config_layout)
        layout.addWidget(config_group)

        # 按钮区域
        button_layout = QHBoxLayout()
        save_btn = StyleManager.create_button("保存", "success")
        save_btn.clicked.connect(self.save_config)
        close_btn = StyleManager.create_button("关闭", "")
        close_btn.clicked.connect(self.close)

        button_layout.addStretch()
        button_layout.addWidget(save_btn)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def save_config(self):
        """保存配置"""
        self.config.set('api.base_url', self.api_url_edit.text())
        self.config.set('api.headers.cookie', self.cookie_edit.toPlainText())
        
        if self.config.save_config():
            CustomMessageBox.show_success(self, "保存成功", "配置已保存")
            self.config_changed.emit()
        else:
            CustomMessageBox.show_error(self, "保存失败", "配置保存失败")
