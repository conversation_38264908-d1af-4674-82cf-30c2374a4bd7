#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
团队管理工具 - 主应用程序入口
支持邀请成员、删除成员、查看团队数据等功能
"""

import sys
from PyQt6.QtWidgets import QApplication, QWidget
from PyQt6.QtCore import QTimer
from PyQt6.QtGui import QFont

# 导入模块化后的组件
from config import Config
from ui import CustomMessageBox
from ui.modern_main_window import ModernMainWindow


def main():
    """主程序入口"""
    app = QApplication(sys.argv)

    # 设置应用信息
    app.setApplicationName("🛠️ 团队管理工具")
    app.setApplicationDisplayName("团队管理工具")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Team Manager")
    app.setOrganizationDomain("teammanager.local")

    # 设置应用样式
    app.setStyle('Fusion')

    # 设置应用字体
    try:
        font = QFont("Microsoft YaHei UI", 9)
        app.setFont(font)
    except:
        # 如果字体设置失败，使用默认字体
        pass

    # 设置应用图标（如果有的话）
    # app.setWindowIcon(QIcon("team_manager_icon.png"))

    try:
        # 创建现代化主窗口
        window = ModernMainWindow()

        # 居中显示窗口
        try:
            screen = app.primaryScreen().geometry()
            window_size = window.geometry()
            x = (screen.width() - window_size.width()) // 2
            y = (screen.height() - window_size.height()) // 2
            window.move(x, y)
        except:
            # 如果居中失败，使用默认位置
            pass

        window.show()

        # 显示启动消息
        try:
            window.status_label.setText("应用启动成功，请配置API设置后开始使用")
        except:
            pass

        print("🎉 团队管理工具启动成功！")
        print("💡 提示：请在 工具 -> 配置设置 中配置API信息")

        # 运行应用
        sys.exit(app.exec())

    except Exception as e:
        # 错误处理
        print(f"❌ 应用启动失败: {str(e)}")
        try:
            # 创建一个临时的父窗口用于显示错误消息
            temp_widget = QWidget()
            CustomMessageBox.show_error(temp_widget, "启动错误",
                                      f"应用启动失败：{str(e)}\n\n"
                                      f"错误详情：\n{str(e)}")
            # 等待一段时间让用户看到错误消息
            QTimer.singleShot(5000, app.quit)
            app.exec()
        except:
            # 如果连错误对话框都无法显示，直接打印错误
            print(f"详细错误信息：{str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
