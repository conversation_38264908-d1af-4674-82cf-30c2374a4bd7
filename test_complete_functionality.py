#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整功能测试脚本
验证所有功能是否正确实现
"""

import sys
import traceback

def test_core_functionality():
    """测试核心功能"""
    print("🧪 测试核心功能...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui import TeamManagerMainWindow
        
        app = QApplication([])
        window = TeamManagerMainWindow()
        
        # 测试主要组件是否存在
        components_to_test = [
            # 邀请成员功能
            ('email_input', '邮箱输入框'),
            ('total_emails_card', '总邮箱统计卡片'),
            ('valid_emails_card', '有效邮箱统计卡片'),
            ('invalid_emails_card', '无效邮箱统计卡片'),
            ('invite_btn', '邀请按钮'),
            ('clear_btn', '清空按钮'),
            ('paste_btn', '粘贴按钮'),
            ('invite_history', '邀请历史'),
            
            # 团队管理功能
            ('members_table', '成员表格'),
            ('invitations_table', '邀请表格'),
            ('total_members_card', '总成员统计卡片'),
            ('active_members_card', '活跃成员统计卡片'),
            ('pending_members_card', '待加入成员统计卡片'),
            ('query_pending_emails_btn', '查询邮箱按钮'),
            
            # 批量操作功能
            ('batch_delete_unjoined_btn', '批量删除未加入按钮'),
            ('batch_delete_invitations_btn', '批量删除邀请按钮'),
            ('batch_delete_all_btn', '批量删除全部按钮'),
            ('switch_to_community_plan_btn', '切换社区计划按钮'),
            ('switch_to_max_plan_btn', '切换Max计划按钮'),
            ('load_credits_btn', '加载积分按钮'),
            ('batch_log', '批量操作日志'),
            
            # 数据视图功能
            ('raw_data_display', '原始数据显示'),
            
            # 头部组件
            ('connection_status', '连接状态'),
            ('refresh_button', '刷新按钮'),
            ('config_button', '配置按钮'),
            ('header_members_stat', '头部成员统计'),
            ('header_invites_stat', '头部邀请统计'),
            ('header_credits_stat', '头部积分统计'),
        ]
        
        missing_components = []
        for attr_name, display_name in components_to_test:
            if not hasattr(window, attr_name):
                missing_components.append(f"  ❌ {display_name} ({attr_name})")
            else:
                print(f"  ✅ {display_name}")
        
        if missing_components:
            print("\n缺失的组件:")
            for component in missing_components:
                print(component)
            return False
        
        # 测试关键方法是否存在
        methods_to_test = [
            ('validate_emails_realtime', '实时邮箱验证'),
            ('invite_members', '邀请成员'),
            ('clear_email_input', '清空邮箱输入'),
            ('paste_clipboard', '粘贴剪贴板'),
            ('batch_delete_unjoined_members', '批量删除未加入成员'),
            ('batch_delete_invitations', '批量删除邀请'),
            ('batch_delete_all_unconfirmed', '批量删除全部未确认'),
            ('switch_to_community_plan', '切换社区计划'),
            ('switch_to_max_plan', '切换Max计划'),
            ('load_credits_data', '加载积分数据'),
            ('query_pending_emails', '查询待处理邮箱'),
            ('start_worker_thread', '启动工作线程'),
            ('update_data_displays', '更新数据显示'),
            ('update_stats_cards', '更新统计卡片'),
            ('log_batch_operation', '记录批量操作'),
            ('log_invite_history', '记录邀请历史'),
        ]
        
        missing_methods = []
        for method_name, display_name in methods_to_test:
            if not hasattr(window, method_name):
                missing_methods.append(f"  ❌ {display_name} ({method_name})")
            else:
                print(f"  ✅ {display_name}")
        
        if missing_methods:
            print("\n缺失的方法:")
            for method in missing_methods:
                print(method)
            return False
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 核心功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_api_functionality():
    """测试API功能"""
    print("\n🌐 测试API功能...")
    
    try:
        from config import Config
        from api import APIClient
        
        config = Config()
        api_client = APIClient(config)
        
        # 测试API方法是否存在
        api_methods = [
            ('validate_email', '邮箱验证'),
            ('get_team_data', '获取团队数据'),
            ('invite_members', '邀请成员'),
            ('delete_member', '删除成员'),
            ('put_user_on_community_plan', '切换社区计划'),
            ('put_user_on_max_plan', '切换Max计划'),
            ('put_user_on_plan', '切换计划'),
            ('get_credits', '获取积分'),
        ]
        
        for method_name, display_name in api_methods:
            if hasattr(api_client, method_name):
                print(f"  ✅ {display_name}")
            else:
                print(f"  ❌ {display_name} ({method_name})")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ API功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_worker_thread():
    """测试工作线程功能"""
    print("\n⚙️ 测试工作线程功能...")
    
    try:
        from utils import WorkerThread
        from config import Config
        from api import APIClient
        
        config = Config()
        api_client = APIClient(config)
        
        # 测试工作线程创建
        worker = WorkerThread(api_client, "get_team_data")
        print("  ✅ 工作线程创建成功")
        
        # 测试信号是否存在
        if hasattr(worker, 'finished') and hasattr(worker, 'progress'):
            print("  ✅ 工作线程信号正常")
        else:
            print("  ❌ 工作线程信号缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 工作线程测试失败: {e}")
        traceback.print_exc()
        return False

def test_ui_dialogs():
    """测试UI对话框"""
    print("\n💬 测试UI对话框...")
    
    try:
        from ui.dialogs import CustomMessageBox, CustomConfirmDialog, ConfigDialog, PendingEmailsDialog
        from config import Config
        
        config = Config()
        
        print("  ✅ CustomMessageBox 导入成功")
        print("  ✅ CustomConfirmDialog 导入成功")
        print("  ✅ ConfigDialog 导入成功")
        print("  ✅ PendingEmailsDialog 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ UI对话框测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始完整功能测试...\n")
    
    tests = [
        ("模块导入", test_core_functionality),
        ("API功能", test_api_functionality),
        ("工作线程", test_worker_thread),
        ("UI对话框", test_ui_dialogs),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"📋 {test_name}测试:")
        if test_func():
            print(f"✅ {test_name}测试通过\n")
            passed += 1
        else:
            print(f"❌ {test_name}测试失败\n")
            break
    
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有功能测试通过！团队管理工具功能完整。")
        print("\n✨ 功能清单:")
        print("  📧 邀请成员功能 - 完整")
        print("  👥 团队管理功能 - 完整")
        print("  ⚡ 批量操作功能 - 完整")
        print("  📊 数据视图功能 - 完整")
        print("  🎨 美化界面设计 - 完整")
        print("  🔧 配置管理功能 - 完整")
        print("  📋 日志系统功能 - 完整")
        return True
    else:
        print("❌ 部分功能测试失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
