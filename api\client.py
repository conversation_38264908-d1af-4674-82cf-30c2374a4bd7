# -*- coding: utf-8 -*-
"""
API客户端模块
"""

import re
import requests
from typing import List, Dict, Tuple, Any
from config.settings import Config


class APIClient:
    """API客户端类"""
    
    def __init__(self, config: Config):
        self.config = config
        self.session = requests.Session()
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = self.config.get('api.headers', {}).copy()
        # 为POST请求添加content-type
        if 'content-type' not in headers:
            headers['content-type'] = 'application/json'
        return headers
    
    def validate_email(self, email: str) -> bool:
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def get_team_data(self) -> <PERSON><PERSON>[bool, Any]:
        """获取团队数据"""
        try:
            url = f"{self.config.get('api.base_url')}/team"
            headers = self._get_headers()

            # 调试信息：打印请求详情
            print(f"🔍 调试信息 - 请求URL: {url}")
            print(f"🔍 调试信息 - 请求头数量: {len(headers)}")

            # 检查Cookie是否存在
            cookie_value = headers.get('cookie', '')
            if cookie_value:
                print(f"🔍 调试信息 - Cookie长度: {len(cookie_value)} 字符")
                print(f"🔍 调试信息 - Cookie前50字符: {cookie_value[:50]}...")
                # 检查关键的认证字段
                if '_session=' in cookie_value:
                    print("✅ 调试信息 - 发现 _session 字段")
                else:
                    print("❌ 调试信息 - 缺少 _session 字段")
            else:
                print("❌ 调试信息 - Cookie为空")

            response = self.session.get(url, headers=headers, timeout=30)

            print(f"🔍 调试信息 - 响应状态码: {response.status_code}")
            print(f"🔍 调试信息 - 响应头: {dict(response.headers)}")

            if response.status_code == 200:
                try:
                    json_data = response.json()
                    print(f"✅ 调试信息 - 成功获取JSON数据，数据类型: {type(json_data)}")
                    if isinstance(json_data, dict):
                        print(f"✅ 调试信息 - JSON数据键: {list(json_data.keys())}")
                    return True, json_data
                except Exception as json_error:
                    print(f"❌ 调试信息 - JSON解析失败: {str(json_error)}")
                    print(f"❌ 调试信息 - 响应内容: {response.text[:500]}...")
                    return False, f"JSON解析失败: {str(json_error)}"
            else:
                error_msg = f"请求失败，状态码: {response.status_code}"
                print(f"❌ 调试信息 - {error_msg}")
                print(f"❌ 调试信息 - 响应内容: {response.text[:500]}...")
                return False, f"{error_msg}\n响应内容: {response.text[:200]}..."
        except Exception as e:
            error_msg = f"网络错误: {str(e)}"
            print(f"❌ 调试信息 - {error_msg}")
            return False, error_msg
    
    def invite_members(self, emails: List[str]) -> Tuple[bool, str]:
        """批量邀请成员"""
        try:
            url = f"{self.config.get('api.base_url')}/team/invite"
            headers = self._get_headers()
            data = {"emails": emails}
            
            response = self.session.post(url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                return True, "邀请发送成功"
            else:
                return False, f"邀请失败，状态码: {response.status_code}\n响应: {response.text}"
        except Exception as e:
            return False, f"网络错误: {str(e)}"
    
    def delete_member(self, member_id: str) -> Tuple[bool, str]:
        """删除单个成员或邀请"""
        try:
            url = f"{self.config.get('api.base_url')}/team/invite/{member_id}"
            headers = self._get_headers()

            response = self.session.delete(url, headers=headers, timeout=30)

            if response.status_code == 200:
                return True, "删除成功"
            else:
                return False, f"删除失败，状态码: {response.status_code}"
        except Exception as e:
            return False, f"网络错误: {str(e)}"

    def put_user_on_community_plan(self) -> Tuple[bool, str]:
        """将登录账号改为 community plan"""
        try:
            url = f"{self.config.get('api.base_url')}/put-user-on-plan"
            headers = self._get_headers()
            data = {"planId": "orb_community_plan"}

            response = self.session.post(url, headers=headers, json=data, timeout=30)

            if response.status_code == 200:
                return True, "账号已成功切换到 Community Plan"
            else:
                return False, f"切换失败，状态码: {response.status_code}\n响应: {response.text}"
        except Exception as e:
            return False, f"网络错误: {str(e)}"

    def put_user_on_max_plan(self) -> Tuple[bool, str]:
        """将登录账号改为 max plan"""
        try:
            url = f"{self.config.get('api.base_url')}/put-user-on-plan"
            headers = self._get_headers()
            data = {"planId": "orb_max_plan"}

            response = self.session.post(url, headers=headers, json=data, timeout=30)

            if response.status_code == 200:
                return True, "账号已成功切换到 Max Plan"
            else:
                return False, f"切换失败，状态码: {response.status_code}\n响应: {response.text}"
        except Exception as e:
            return False, f"网络错误: {str(e)}"

    def put_user_on_plan(self, plan_id: str) -> Tuple[bool, str]:
        """将登录账号改为指定计划"""
        try:
            url = f"{self.config.get('api.base_url')}/put-user-on-plan"
            headers = self._get_headers()
            data = {"planId": plan_id}

            response = self.session.post(url, headers=headers, json=data, timeout=30)

            if response.status_code == 200:
                return True, f"账号已成功切换到 {plan_id}"
            else:
                return False, f"切换失败，状态码: {response.status_code}\n响应: {response.text}"
        except Exception as e:
            return False, f"网络错误: {str(e)}"

    def get_credits(self) -> Tuple[bool, Any]:
        """获取积分信息"""
        try:
            url = f"{self.config.get('api.base_url')}/credits"
            headers = self._get_headers()

            # 调试信息：打印请求详情
            print(f"🔍 调试信息 - 积分请求URL: {url}")
            print(f"🔍 调试信息 - 请求头数量: {len(headers)}")

            response = self.session.get(url, headers=headers, timeout=30)

            print(f"🔍 调试信息 - 积分响应状态码: {response.status_code}")

            if response.status_code == 200:
                try:
                    json_data = response.json()
                    print(f"✅ 调试信息 - 成功获取积分数据: {json_data}")
                    return True, json_data
                except Exception as json_error:
                    print(f"❌ 调试信息 - 积分JSON解析失败: {str(json_error)}")
                    print(f"❌ 调试信息 - 响应内容: {response.text[:500]}...")
                    return False, f"JSON解析失败: {str(json_error)}"
            else:
                error_msg = f"积分请求失败，状态码: {response.status_code}"
                print(f"❌ 调试信息 - {error_msg}")
                print(f"❌ 调试信息 - 响应内容: {response.text[:500]}...")
                return False, f"{error_msg}\n响应内容: {response.text[:200]}..."
        except Exception as e:
            error_msg = f"积分网络错误: {str(e)}"
            print(f"❌ 调试信息 - {error_msg}")
            return False, error_msg
