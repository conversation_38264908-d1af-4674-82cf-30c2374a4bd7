# -*- coding: utf-8 -*-
"""
现代化主窗口 - 全新设计
支持垂直滚动、多页布局、现代化UI
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QTextEdit, QFrame, QApplication, QStackedWidget,
    QSystemTrayIcon, QMenu, QProgressBar, QScrollArea, QSizePolicy
)
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QIcon, QAction, QFont

from config.settings import Config
from api.client import APIClient
from utils.threads import WorkerThread
from .modern_styles import ModernStyleManager
from .dialogs import CustomMessageBox, ConfigDialog


class ModernMainWindow(QMainWindow):
    """现代化主窗口 - 支持垂直滚动和多页布局"""

    def __init__(self):
        super().__init__()
        self.config = Config()
        self.api_client = APIClient(self.config)
        self.team_data = None
        self.credits_data = None
        self.worker_thread = None
        self.is_connected = False
        self.current_page = 0

        # 窗口设置
        self.setWindowTitle("🛠️ 团队管理工具 - 现代化版本")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 800)
        
        # 应用现代化样式
        app = QApplication.instance()
        app.setStyleSheet(ModernStyleManager.get_app_style())
        
        # 设置字体
        font = QFont("Microsoft YaHei UI", 10)
        app.setFont(font)
        
        # 设置系统托盘
        self.setup_tray_icon()
        
        # 初始化UI
        self.init_ui()
        self.init_status_bar()
        
        # 设置刷新定时器
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.auto_refresh_data)
        self.update_refresh_timer()
        
        # 初始化日志
        self.log_entries = []
        QTimer.singleShot(100, lambda: self.log_info("系统", "现代化团队管理工具已启动"))

    def setup_tray_icon(self):
        """设置系统托盘图标"""
        self.tray_icon = QSystemTrayIcon(self)
        
        try:
            icon = QIcon("team_manager_icon.png")
            if icon.isNull():
                icon = QIcon.fromTheme("applications-system")
            self.tray_icon.setIcon(icon)
        except Exception as e:
            print(f"无法加载托盘图标: {e}")
        
        # 创建托盘菜单
        tray_menu = QMenu()
        
        show_action = QAction("显示", self)
        show_action.triggered.connect(self.show_from_tray)
        tray_menu.addAction(show_action)
        
        hide_action = QAction("隐藏", self)
        hide_action.triggered.connect(self.hide)
        tray_menu.addAction(hide_action)
        
        tray_menu.addSeparator()
        
        refresh_action = QAction("刷新数据", self)
        refresh_action.triggered.connect(self.refresh_team_data)
        tray_menu.addAction(refresh_action)
        
        tray_menu.addSeparator()
        
        quit_action = QAction("退出", self)
        quit_action.triggered.connect(self.quit_from_tray)
        tray_menu.addAction(quit_action)
        
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.activated.connect(self.tray_icon_activated)
        self.tray_icon.show()

    def tray_icon_activated(self, reason):
        """处理托盘图标激活事件"""
        if reason == QSystemTrayIcon.ActivationReason.Trigger:
            if self.isHidden():
                self.show_from_tray()
            else:
                self.hide()
    
    def show_from_tray(self):
        """从托盘显示窗口"""
        self.show()
        self.activateWindow()
        
    def quit_from_tray(self):
        """从托盘退出程序"""
        QApplication.quit()

    def init_ui(self):
        """初始化现代化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 垂直布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建顶部标题栏
        header = self.create_modern_header()
        main_layout.addWidget(header)

        # 创建导航栏
        navigation = self.create_navigation_bar()
        main_layout.addWidget(navigation)

        # 创建主内容区域（支持滚动）
        content_area = self.create_content_area()
        main_layout.addWidget(content_area, 1)  # 占据剩余空间

        # 创建底部状态栏
        footer = self.create_footer()
        main_layout.addWidget(footer)

        central_widget.setLayout(main_layout)

    def create_modern_header(self):
        """创建现代化顶部标题栏"""
        header = QFrame()
        header.setFixedHeight(100)
        header.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {ModernStyleManager.PRIMARY},
                    stop:0.5 {ModernStyleManager.PRIMARY_LIGHT},
                    stop:1 {ModernStyleManager.SECONDARY});
                border: none;
                border-bottom: 1px solid {ModernStyleManager.PRIMARY}40;
            }}
        """)

        layout = QHBoxLayout(header)
        layout.setContentsMargins(32, 16, 32, 16)
        layout.setSpacing(24)

        # 左侧 - Logo和标题
        left_section = QWidget()
        left_layout = QHBoxLayout(left_section)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(16)

        # Logo
        logo_label = QLabel("🛠️")
        logo_label.setStyleSheet("""
            font-size: 32px;
            color: white;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 12px;
            min-width: 40px;
            min-height: 40px;
        """)
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 标题信息
        title_widget = QWidget()
        title_layout = QVBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(2)

        app_title = QLabel("团队管理工具")
        app_title.setStyleSheet("""
            font-size: 24px;
            font-weight: 800;
            color: white;
            margin: 0;
        """)

        app_subtitle = QLabel("Modern Team Management Tool")
        app_subtitle.setStyleSheet("""
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
        """)

        title_layout.addWidget(app_title)
        title_layout.addWidget(app_subtitle)

        left_layout.addWidget(logo_label)
        left_layout.addWidget(title_widget)

        # 右侧 - 快速操作
        right_section = QWidget()
        right_layout = QHBoxLayout(right_section)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(12)

        # 连接状态
        self.connection_status = QLabel("🔴 未连接")
        self.connection_status.setStyleSheet("""
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            padding: 8px 16px;
            color: white;
            font-size: 13px;
            font-weight: 600;
        """)

        # 快速操作按钮
        self.refresh_btn = ModernStyleManager.create_modern_button("🔄", "ghost", size="small")
        self.refresh_btn.clicked.connect(self.refresh_team_data)
        
        self.settings_btn = ModernStyleManager.create_modern_button("⚙️", "ghost", size="small")
        self.settings_btn.clicked.connect(self.open_config_dialog)

        right_layout.addWidget(self.connection_status)
        right_layout.addWidget(self.refresh_btn)
        right_layout.addWidget(self.settings_btn)

        layout.addWidget(left_section)
        layout.addStretch()
        layout.addWidget(right_section)

        return header

    def create_navigation_bar(self):
        """创建现代化导航栏"""
        nav_bar = QFrame()
        nav_bar.setFixedHeight(70)
        nav_bar.setStyleSheet(f"""
            QFrame {{
                background: {ModernStyleManager.get_color('surface')};
                border: none;
                border-bottom: 1px solid {ModernStyleManager.get_color('border')};
            }}
        """)

        layout = QHBoxLayout(nav_bar)
        layout.setContentsMargins(32, 12, 32, 12)
        layout.setSpacing(8)

        # 导航按钮
        self.nav_buttons = []
        nav_items = [
            ("📊", "仪表板", 0),
            ("👥", "邀请成员", 1),
            ("🔧", "团队管理", 2),
            ("⚡", "批量操作", 3),
            ("📈", "数据视图", 4),
            ("⚙️", "设置", 5)
        ]

        for icon, text, index in nav_items:
            btn = ModernStyleManager.create_navigation_button(text, icon, index == 0)
            btn.clicked.connect(lambda checked=False, idx=index: self.switch_page(idx))
            self.nav_buttons.append(btn)
            layout.addWidget(btn)

        layout.addStretch()
        return nav_bar

    def create_content_area(self):
        """创建主内容区域（支持垂直滚动）"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)

        # 创建堆叠窗口
        self.stacked_widget = QStackedWidget()

        # 创建各个页面
        self.create_pages()

        scroll_area.setWidget(self.stacked_widget)
        return scroll_area

    def create_pages(self):
        """创建所有功能页面"""
        # 仪表板页面
        dashboard_page = self.create_dashboard_page()
        self.stacked_widget.addWidget(dashboard_page)

        # 邀请成员页面
        invite_page = self.create_invite_page()
        self.stacked_widget.addWidget(invite_page)

        # 团队管理页面
        manage_page = self.create_manage_page()
        self.stacked_widget.addWidget(manage_page)

        # 批量操作页面
        batch_page = self.create_batch_page()
        self.stacked_widget.addWidget(batch_page)

        # 数据视图页面
        data_page = self.create_data_page()
        self.stacked_widget.addWidget(data_page)

        # 设置页面
        settings_page = self.create_settings_page()
        self.stacked_widget.addWidget(settings_page)

    def create_dashboard_page(self):
        """创建仪表板页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(32, 24, 32, 24)
        layout.setSpacing(24)

        # 页面标题
        title_label = QLabel("📊 仪表板")
        title_label.setStyleSheet(f"""
            font-size: 28px;
            font-weight: 800;
            color: {ModernStyleManager.get_color('text')};
            margin-bottom: 8px;
        """)
        layout.addWidget(title_label)

        # 统计卡片区域
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(20)

        # 创建统计卡片
        self.members_stat = ModernStyleManager.create_stat_card(
            "👥", "团队成员", "0", "当前活跃成员", ModernStyleManager.PRIMARY
        )
        self.invites_stat = ModernStyleManager.create_stat_card(
            "📧", "待处理邀请", "0", "等待接受的邀请", ModernStyleManager.INFO
        )
        self.credits_stat = ModernStyleManager.create_stat_card(
            "💎", "可用积分", "0", "当前账户余额", ModernStyleManager.SUCCESS
        )
        self.today_invites_stat = ModernStyleManager.create_stat_card(
            "📅", "今日邀请", "0", "今天发送的邀请", ModernStyleManager.WARNING
        )

        stats_layout.addWidget(self.members_stat)
        stats_layout.addWidget(self.invites_stat)
        stats_layout.addWidget(self.credits_stat)
        stats_layout.addWidget(self.today_invites_stat)

        layout.addLayout(stats_layout)

        # 快速操作区域
        quick_actions_card = ModernStyleManager.create_card("⚡ 快速操作", icon="🚀")
        quick_actions_content = QWidget()
        quick_layout = QHBoxLayout(quick_actions_content)
        quick_layout.setSpacing(16)

        # 快速操作按钮
        invite_btn = ModernStyleManager.create_modern_button("邀请成员", "primary", "👥")
        invite_btn.clicked.connect(lambda: self.switch_page(1))

        manage_btn = ModernStyleManager.create_modern_button("管理团队", "secondary", "🔧")
        manage_btn.clicked.connect(lambda: self.switch_page(2))

        batch_btn = ModernStyleManager.create_modern_button("批量操作", "info", "⚡")
        batch_btn.clicked.connect(lambda: self.switch_page(3))

        refresh_btn = ModernStyleManager.create_modern_button("刷新数据", "success", "🔄")
        refresh_btn.clicked.connect(self.refresh_team_data)

        quick_layout.addWidget(invite_btn)
        quick_layout.addWidget(manage_btn)
        quick_layout.addWidget(batch_btn)
        quick_layout.addWidget(refresh_btn)
        quick_layout.addStretch()

        quick_actions_card.layout().addWidget(quick_actions_content)
        layout.addWidget(quick_actions_card)

        # 系统状态区域
        status_card = ModernStyleManager.create_card("📊 系统状态", icon="💻")
        status_content = QWidget()
        status_layout = QVBoxLayout(status_content)

        # 连接状态
        self.dashboard_connection_status = QLabel("🔴 未连接到服务器")
        self.dashboard_connection_status.setStyleSheet(f"""
            font-size: 14px;
            color: {ModernStyleManager.ERROR};
            padding: 8px;
            background: {ModernStyleManager.ERROR}10;
            border-radius: 6px;
        """)

        # 最后更新时间
        self.last_update_label = QLabel("最后更新：从未")
        self.last_update_label.setStyleSheet(f"""
            font-size: 13px;
            color: {ModernStyleManager.get_color('text_secondary')};
            margin-top: 8px;
        """)

        status_layout.addWidget(self.dashboard_connection_status)
        status_layout.addWidget(self.last_update_label)

        status_card.layout().addWidget(status_content)
        layout.addWidget(status_card)

        layout.addStretch()
        return page

    def create_invite_page(self):
        """创建邀请成员页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(32, 24, 32, 24)
        layout.setSpacing(24)

        # 页面标题
        title_label = QLabel("👥 邀请成员")
        title_label.setStyleSheet(f"""
            font-size: 28px;
            font-weight: 800;
            color: {ModernStyleManager.get_color('text')};
            margin-bottom: 8px;
        """)
        layout.addWidget(title_label)

        # 邀请表单卡片
        invite_card = ModernStyleManager.create_card("📧 发送邀请", icon="✉️")
        invite_content = QWidget()
        invite_layout = QVBoxLayout(invite_content)

        # 邮箱输入
        email_label = QLabel("邮箱地址：")
        email_label.setStyleSheet(f"font-weight: 600; color: {ModernStyleManager.get_color('text')};")

        from PyQt6.QtWidgets import QLineEdit
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("请输入要邀请的邮箱地址...")

        # 发送按钮
        send_btn = ModernStyleManager.create_modern_button("发送邀请", "primary", "📤")
        send_btn.clicked.connect(self.send_invitation)

        invite_layout.addWidget(email_label)
        invite_layout.addWidget(self.email_input)
        invite_layout.addWidget(send_btn)

        invite_card.layout().addWidget(invite_content)
        layout.addWidget(invite_card)

        # 批量邀请卡片
        batch_invite_card = ModernStyleManager.create_card("📋 批量邀请", icon="📝")
        batch_content = QWidget()
        batch_layout = QVBoxLayout(batch_content)

        batch_label = QLabel("批量邮箱（每行一个）：")
        batch_label.setStyleSheet(f"font-weight: 600; color: {ModernStyleManager.get_color('text')};")

        from PyQt6.QtWidgets import QTextEdit
        self.batch_email_input = QTextEdit()
        self.batch_email_input.setPlaceholderText("请输入多个邮箱地址，每行一个...")
        self.batch_email_input.setMaximumHeight(120)

        batch_send_btn = ModernStyleManager.create_modern_button("批量发送", "secondary", "📬")
        batch_send_btn.clicked.connect(self.send_batch_invitations)

        batch_layout.addWidget(batch_label)
        batch_layout.addWidget(self.batch_email_input)
        batch_layout.addWidget(batch_send_btn)

        batch_invite_card.layout().addWidget(batch_content)
        layout.addWidget(batch_invite_card)

        layout.addStretch()
        return page

    def create_manage_page(self):
        """创建团队管理页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(32, 24, 32, 24)
        layout.setSpacing(24)

        # 页面标题
        title_label = QLabel("🔧 团队管理")
        title_label.setStyleSheet(f"""
            font-size: 28px;
            font-weight: 800;
            color: {ModernStyleManager.get_color('text')};
            margin-bottom: 8px;
        """)
        layout.addWidget(title_label)

        # 团队成员列表卡片
        members_card = ModernStyleManager.create_card("👥 团队成员", icon="👨‍👩‍👧‍👦")
        members_content = QWidget()
        members_layout = QVBoxLayout(members_content)

        # 这里将添加成员列表表格
        members_placeholder = QLabel("团队成员列表将在这里显示...")
        members_placeholder.setStyleSheet(f"""
            color: {ModernStyleManager.get_color('text_secondary')};
            font-style: italic;
            padding: 40px;
            text-align: center;
        """)
        members_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)

        members_layout.addWidget(members_placeholder)
        members_card.layout().addWidget(members_content)
        layout.addWidget(members_card)

        layout.addStretch()
        return page

    def create_batch_page(self):
        """创建批量操作页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(32, 24, 32, 24)
        layout.setSpacing(24)

        # 页面标题
        title_label = QLabel("⚡ 批量操作")
        title_label.setStyleSheet(f"""
            font-size: 28px;
            font-weight: 800;
            color: {ModernStyleManager.get_color('text')};
            margin-bottom: 8px;
        """)
        layout.addWidget(title_label)

        # 批量操作卡片
        batch_card = ModernStyleManager.create_card("🔄 批量操作", icon="⚡")
        batch_content = QWidget()
        batch_layout = QVBoxLayout(batch_content)

        batch_placeholder = QLabel("批量操作功能将在这里显示...")
        batch_placeholder.setStyleSheet(f"""
            color: {ModernStyleManager.get_color('text_secondary')};
            font-style: italic;
            padding: 40px;
            text-align: center;
        """)
        batch_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)

        batch_layout.addWidget(batch_placeholder)
        batch_card.layout().addWidget(batch_content)
        layout.addWidget(batch_card)

        layout.addStretch()
        return page

    def create_data_page(self):
        """创建数据视图页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(32, 24, 32, 24)
        layout.setSpacing(24)

        # 页面标题
        title_label = QLabel("📈 数据视图")
        title_label.setStyleSheet(f"""
            font-size: 28px;
            font-weight: 800;
            color: {ModernStyleManager.get_color('text')};
            margin-bottom: 8px;
        """)
        layout.addWidget(title_label)

        # 数据视图卡片
        data_card = ModernStyleManager.create_card("📊 数据分析", icon="📈")
        data_content = QWidget()
        data_layout = QVBoxLayout(data_content)

        data_placeholder = QLabel("数据视图和分析将在这里显示...")
        data_placeholder.setStyleSheet(f"""
            color: {ModernStyleManager.get_color('text_secondary')};
            font-style: italic;
            padding: 40px;
            text-align: center;
        """)
        data_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)

        data_layout.addWidget(data_placeholder)
        data_card.layout().addWidget(data_content)
        layout.addWidget(data_card)

        layout.addStretch()
        return page

    def create_settings_page(self):
        """创建设置页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(32, 24, 32, 24)
        layout.setSpacing(24)

        # 页面标题
        title_label = QLabel("⚙️ 设置")
        title_label.setStyleSheet(f"""
            font-size: 28px;
            font-weight: 800;
            color: {ModernStyleManager.get_color('text')};
            margin-bottom: 8px;
        """)
        layout.addWidget(title_label)

        # 设置卡片
        settings_card = ModernStyleManager.create_card("🔧 应用设置", icon="⚙️")
        settings_content = QWidget()
        settings_layout = QVBoxLayout(settings_content)

        # 主题切换
        theme_btn = ModernStyleManager.create_modern_button("切换主题", "outline", "🌙")
        theme_btn.clicked.connect(self.toggle_theme)

        # 配置按钮
        config_btn = ModernStyleManager.create_modern_button("打开配置", "primary", "🔧")
        config_btn.clicked.connect(self.open_config_dialog)

        settings_layout.addWidget(theme_btn)
        settings_layout.addWidget(config_btn)

        settings_card.layout().addWidget(settings_content)
        layout.addWidget(settings_card)

        layout.addStretch()
        return page

    def create_footer(self):
        """创建底部状态栏"""
        footer = QFrame()
        footer.setFixedHeight(40)
        footer.setStyleSheet(f"""
            QFrame {{
                background: {ModernStyleManager.get_color('bg_secondary')};
                border: none;
                border-top: 1px solid {ModernStyleManager.get_color('border')};
            }}
        """)

        layout = QHBoxLayout(footer)
        layout.setContentsMargins(32, 8, 32, 8)

        # 状态信息
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet(f"""
            color: {ModernStyleManager.get_color('text_secondary')};
            font-size: 12px;
        """)

        # 版本信息
        version_label = QLabel("v1.0 - 现代化版本")
        version_label.setStyleSheet(f"""
            color: {ModernStyleManager.get_color('text_tertiary')};
            font-size: 11px;
        """)

        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(version_label)

        return footer

    def switch_page(self, index):
        """切换页面"""
        if index == self.current_page:
            return

        # 更新导航按钮状态
        for i, btn in enumerate(self.nav_buttons):
            is_active = (i == index)
            btn.setStyleSheet(ModernStyleManager.create_navigation_button("", "", is_active).styleSheet())

        # 切换页面
        self.stacked_widget.setCurrentIndex(index)
        self.current_page = index

        # 更新状态
        page_names = ["仪表板", "邀请成员", "团队管理", "批量操作", "数据视图", "设置"]
        if index < len(page_names):
            self.status_label.setText(f"当前页面：{page_names[index]}")

    def init_status_bar(self):
        """初始化状态栏（兼容性方法）"""
        pass  # 状态栏已在footer中实现

    def toggle_theme(self):
        """切换主题"""
        current_theme = ModernStyleManager.current_theme
        new_theme = "dark" if current_theme == "light" else "light"
        ModernStyleManager.switch_theme(new_theme)

        # 重新应用样式
        app = QApplication.instance()
        app.setStyleSheet(ModernStyleManager.get_app_style())

        self.status_label.setText(f"已切换到{'深色' if new_theme == 'dark' else '浅色'}主题")

    def open_config_dialog(self):
        """打开配置对话框"""
        dialog = ConfigDialog(self.config, self)
        dialog.config_changed.connect(self.apply_config)
        dialog.show()

    def apply_config(self):
        """应用新的配置设置"""
        print("正在应用新的配置设置...")
        self.api_client = APIClient(self.config)
        self.status_label.setText("配置已更新")

    def refresh_team_data(self):
        """刷新团队数据"""
        self.status_label.setText("正在刷新数据...")
        # 这里将调用实际的数据刷新逻辑
        QTimer.singleShot(1000, lambda: self.status_label.setText("数据刷新完成"))

    def send_invitation(self):
        """发送单个邀请"""
        email = self.email_input.text().strip()
        if not email:
            self.status_label.setText("请输入邮箱地址")
            return

        self.status_label.setText(f"正在发送邀请到 {email}...")
        # 这里将调用实际的邀请发送逻辑
        QTimer.singleShot(1000, lambda: self.status_label.setText("邀请发送成功"))
        self.email_input.clear()

    def send_batch_invitations(self):
        """发送批量邀请"""
        emails_text = self.batch_email_input.toPlainText().strip()
        if not emails_text:
            self.status_label.setText("请输入邮箱地址")
            return

        emails = [email.strip() for email in emails_text.split('\n') if email.strip()]
        self.status_label.setText(f"正在批量发送 {len(emails)} 个邀请...")
        # 这里将调用实际的批量邀请发送逻辑
        QTimer.singleShot(2000, lambda: self.status_label.setText(f"批量邀请发送完成，共 {len(emails)} 个"))
        self.batch_email_input.clear()

    def update_refresh_timer(self):
        """更新刷新定时器"""
        # 兼容性方法
        pass

    def auto_refresh_data(self):
        """自动刷新数据"""
        # 兼容性方法
        pass

    def log_info(self, category, message):
        """记录日志信息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {category}: {message}"
        self.log_entries.append(log_entry)
        print(log_entry)  # 临时输出到控制台
