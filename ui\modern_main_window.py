# -*- coding: utf-8 -*-
"""
现代化主窗口 - 全新设计
支持垂直滚动、多页布局、现代化UI
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QTextEdit, QFrame, QApplication, QStackedWidget,
    QSystemTrayIcon, QMenu, QProgressBar, QScrollArea, QSizePolicy,
    QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit
)
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QIcon, QAction, QFont

from config.settings import Config
from api.client import APIClient
from utils.threads import WorkerThread
from .modern_styles import ModernStyleManager
from .dialogs import CustomMessageBox, ConfigDialog


class ModernMainWindow(QMainWindow):
    """现代化主窗口 - 支持垂直滚动和多页布局"""

    def __init__(self):
        super().__init__()
        self.config = Config()
        self.api_client = APIClient(self.config)
        self.team_data = None
        self.credits_data = None
        self.worker_thread = None
        self.is_connected = False
        self.current_page = 0

        # 窗口设置
        self.setWindowTitle("🛠️ 团队管理工具 - 现代化版本")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 800)
        
        # 应用现代化样式
        app = QApplication.instance()
        app.setStyleSheet(ModernStyleManager.get_app_style())
        
        # 设置字体
        font = QFont("Microsoft YaHei UI", 10)
        app.setFont(font)
        
        # 设置系统托盘
        self.setup_tray_icon()
        
        # 初始化UI
        self.init_ui()
        self.init_status_bar()
        
        # 设置刷新定时器
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.auto_refresh_data)
        self.update_refresh_timer()
        
        # 初始化日志
        self.log_entries = []
        QTimer.singleShot(100, lambda: self.log_info("系统", "现代化团队管理工具已启动"))

    def setup_tray_icon(self):
        """设置系统托盘图标"""
        self.tray_icon = QSystemTrayIcon(self)
        
        try:
            icon = QIcon("team_manager_icon.png")
            if icon.isNull():
                icon = QIcon.fromTheme("applications-system")
            self.tray_icon.setIcon(icon)
        except Exception as e:
            print(f"无法加载托盘图标: {e}")
        
        # 创建托盘菜单
        tray_menu = QMenu()
        
        show_action = QAction("显示", self)
        show_action.triggered.connect(self.show_from_tray)
        tray_menu.addAction(show_action)
        
        hide_action = QAction("隐藏", self)
        hide_action.triggered.connect(self.hide)
        tray_menu.addAction(hide_action)
        
        tray_menu.addSeparator()
        
        refresh_action = QAction("刷新数据", self)
        refresh_action.triggered.connect(self.refresh_team_data)
        tray_menu.addAction(refresh_action)
        
        tray_menu.addSeparator()
        
        quit_action = QAction("退出", self)
        quit_action.triggered.connect(self.quit_from_tray)
        tray_menu.addAction(quit_action)
        
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.activated.connect(self.tray_icon_activated)
        self.tray_icon.show()

    def tray_icon_activated(self, reason):
        """处理托盘图标激活事件"""
        if reason == QSystemTrayIcon.ActivationReason.Trigger:
            if self.isHidden():
                self.show_from_tray()
            else:
                self.hide()
    
    def show_from_tray(self):
        """从托盘显示窗口"""
        self.show()
        self.activateWindow()
        
    def quit_from_tray(self):
        """从托盘退出程序"""
        QApplication.quit()

    def init_ui(self):
        """初始化超美观现代化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 水平布局（侧边栏 + 主内容）
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建侧边栏
        sidebar = self.create_beautiful_sidebar()
        main_layout.addWidget(sidebar)

        # 创建主内容区域
        content_area = self.create_main_content_area()
        main_layout.addWidget(content_area, 1)  # 占据剩余空间

        central_widget.setLayout(main_layout)

    def create_beautiful_sidebar(self):
        """创建超美观侧边栏"""
        sidebar = QFrame()
        sidebar.setFixedWidth(280)
        sidebar.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {ModernStyleManager.PRIMARY_GRADIENT_START},
                    stop:0.3 {ModernStyleManager.PRIMARY_GRADIENT_END},
                    stop:0.7 {ModernStyleManager.SECONDARY_GRADIENT_START},
                    stop:1 {ModernStyleManager.SECONDARY_GRADIENT_END});
                border: none;
                border-right: 1px solid rgba(255, 255, 255, 0.1);
            }}
        """)

        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(24, 32, 24, 24)
        layout.setSpacing(32)

        # Logo和标题区域
        header_section = self.create_sidebar_header()
        layout.addWidget(header_section)

        # 导航菜单
        nav_section = self.create_sidebar_navigation()
        layout.addWidget(nav_section)

        layout.addStretch()

        # 底部用户信息
        user_section = self.create_sidebar_footer()
        layout.addWidget(user_section)

        return sidebar

    def create_sidebar_header(self):
        """创建侧边栏头部"""
        header = QWidget()
        layout = QVBoxLayout(header)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(12)

        # Logo
        logo_label = QLabel("🛠️")
        logo_label.setStyleSheet("""
            font-size: 40px;
            color: white;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 20px;
            padding: 16px;
            min-width: 72px;
            min-height: 72px;
        """)
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 应用标题
        title_label = QLabel("团队管理工具")
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: 800;
            color: white;
            margin: 8px 0 0 0;
            letter-spacing: -0.5px;
        """)

        # 副标题
        subtitle_label = QLabel("Modern Team Manager")
        subtitle_label.setStyleSheet("""
            font-size: 13px;
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
            font-weight: 400;
        """)

        layout.addWidget(logo_label, 0, Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label, 0, Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(subtitle_label, 0, Qt.AlignmentFlag.AlignCenter)

        return header

    def create_sidebar_navigation(self):
        """创建侧边栏导航"""
        nav_widget = QWidget()
        layout = QVBoxLayout(nav_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        # 导航项目
        nav_items = [
            ("📊", "仪表板", 0),
            ("👥", "邀请成员", 1),
            ("🔧", "团队管理", 2),
            ("⚡", "批量操作", 3),
            ("📈", "数据视图", 4),
            ("⚙️", "设置", 5)
        ]

        self.nav_buttons = []
        for icon, text, index in nav_items:
            btn = self.create_sidebar_nav_button(icon, text, index == 0)
            btn.clicked.connect(lambda checked=False, idx=index: self.switch_page(idx))
            self.nav_buttons.append(btn)
            layout.addWidget(btn)

        return nav_widget

    def create_sidebar_nav_button(self, icon, text, is_active=False):
        """创建侧边栏导航按钮"""
        from PyQt6.QtWidgets import QPushButton
        button = QPushButton(f"{icon}  {text}")

        if is_active:
            button.setStyleSheet("""
                QPushButton {
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 12px;
                    padding: 16px 20px;
                    font-size: 15px;
                    font-weight: 600;
                    text-align: left;
                    min-height: 20px;
                }

                QPushButton:hover {
                    background: rgba(255, 255, 255, 0.25);
                    border: 1px solid rgba(255, 255, 255, 0.4);
                }
            """)
        else:
            button.setStyleSheet("""
                QPushButton {
                    background: transparent;
                    color: rgba(255, 255, 255, 0.8);
                    border: 1px solid transparent;
                    border-radius: 12px;
                    padding: 16px 20px;
                    font-size: 15px;
                    font-weight: 500;
                    text-align: left;
                    min-height: 20px;
                }

                QPushButton:hover {
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                    border: 1px solid rgba(255, 255, 255, 0.2);
                }
            """)

        return button

    def create_sidebar_footer(self):
        """创建侧边栏底部"""
        footer = QWidget()
        layout = QVBoxLayout(footer)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(12)

        # 连接状态
        self.sidebar_status = QLabel("🔴 未连接")
        self.sidebar_status.setStyleSheet("""
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px 16px;
            color: white;
            font-size: 13px;
            font-weight: 500;
        """)
        self.sidebar_status.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 快速操作按钮
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(8)

        refresh_btn = ModernStyleManager.create_modern_button("🔄", "ghost", size="small")
        refresh_btn.clicked.connect(self.refresh_team_data)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.1);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 8px;
                font-size: 16px;
                min-width: 36px;
                min-height: 36px;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.2);
            }
        """)

        settings_btn = ModernStyleManager.create_modern_button("⚙️", "ghost", size="small")
        settings_btn.clicked.connect(self.open_config_dialog)
        settings_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.1);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 8px;
                font-size: 16px;
                min-width: 36px;
                min-height: 36px;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.2);
            }
        """)

        actions_layout.addWidget(refresh_btn)
        actions_layout.addWidget(settings_btn)

        layout.addWidget(self.sidebar_status)
        layout.addLayout(actions_layout)

        return footer

    def create_main_content_area(self):
        """创建主内容区域"""
        content_widget = QWidget()
        content_widget.setStyleSheet(f"""
            QWidget {{
                background: {ModernStyleManager.BG_MAIN};
            }}
        """)

        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 顶部栏
        top_bar = self.create_top_bar()
        layout.addWidget(top_bar)

        # 主内容区域（支持滚动）
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)

        # 创建堆叠窗口
        self.stacked_widget = QStackedWidget()
        self.create_pages()

        scroll_area.setWidget(self.stacked_widget)
        layout.addWidget(scroll_area, 1)

        return content_widget

    def create_top_bar(self):
        """创建顶部栏"""
        top_bar = QFrame()
        top_bar.setFixedHeight(80)
        top_bar.setStyleSheet(f"""
            QFrame {{
                background: {ModernStyleManager.SURFACE_PRIMARY};
                border: none;
                border-bottom: 1px solid {ModernStyleManager.BORDER_LIGHT};
            }}
        """)

        layout = QHBoxLayout(top_bar)
        layout.setContentsMargins(40, 16, 40, 16)
        layout.setSpacing(24)

        # 页面标题
        self.page_title = QLabel("📊 仪表板")
        self.page_title.setStyleSheet(f"""
            font-size: 28px;
            font-weight: 800;
            color: {ModernStyleManager.TEXT_PRIMARY};
            margin: 0;
            letter-spacing: -0.5px;
        """)

        # 右侧操作区域
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(16)

        # 搜索框（占位）
        search_placeholder = QLabel("🔍 搜索功能")
        search_placeholder.setStyleSheet(f"""
            background: {ModernStyleManager.BG_SECONDARY};
            border: 1px solid {ModernStyleManager.BORDER_LIGHT};
            border-radius: 8px;
            padding: 8px 16px;
            color: {ModernStyleManager.TEXT_TERTIARY};
            font-size: 13px;
        """)

        # 状态标签
        self.top_status_label = QLabel("就绪")
        self.top_status_label.setStyleSheet(f"""
            color: {ModernStyleManager.TEXT_SECONDARY};
            font-size: 14px;
            font-weight: 500;
        """)

        actions_layout.addWidget(search_placeholder)
        actions_layout.addWidget(self.top_status_label)

        layout.addWidget(self.page_title)
        layout.addStretch()
        layout.addWidget(actions_widget)

        return top_bar

    def create_modern_header(self):
        """创建现代化顶部标题栏"""
        header = QFrame()
        header.setFixedHeight(100)
        header.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {ModernStyleManager.PRIMARY},
                    stop:0.5 {ModernStyleManager.PRIMARY_LIGHT},
                    stop:1 {ModernStyleManager.SECONDARY});
                border: none;
                border-bottom: 1px solid {ModernStyleManager.PRIMARY}40;
            }}
        """)

        layout = QHBoxLayout(header)
        layout.setContentsMargins(32, 16, 32, 16)
        layout.setSpacing(24)

        # 左侧 - Logo和标题
        left_section = QWidget()
        left_layout = QHBoxLayout(left_section)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(16)

        # Logo
        logo_label = QLabel("🛠️")
        logo_label.setStyleSheet("""
            font-size: 32px;
            color: white;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 12px;
            min-width: 40px;
            min-height: 40px;
        """)
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 标题信息
        title_widget = QWidget()
        title_layout = QVBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(2)

        app_title = QLabel("团队管理工具")
        app_title.setStyleSheet("""
            font-size: 24px;
            font-weight: 800;
            color: white;
            margin: 0;
        """)

        app_subtitle = QLabel("Modern Team Management Tool")
        app_subtitle.setStyleSheet("""
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
        """)

        title_layout.addWidget(app_title)
        title_layout.addWidget(app_subtitle)

        left_layout.addWidget(logo_label)
        left_layout.addWidget(title_widget)

        # 右侧 - 快速操作
        right_section = QWidget()
        right_layout = QHBoxLayout(right_section)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(12)

        # 连接状态
        self.connection_status = QLabel("🔴 未连接")
        self.connection_status.setStyleSheet("""
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            padding: 8px 16px;
            color: white;
            font-size: 13px;
            font-weight: 600;
        """)

        # 快速操作按钮
        self.refresh_btn = ModernStyleManager.create_modern_button("🔄", "ghost", size="small")
        self.refresh_btn.clicked.connect(self.refresh_team_data)
        
        self.settings_btn = ModernStyleManager.create_modern_button("⚙️", "ghost", size="small")
        self.settings_btn.clicked.connect(self.open_config_dialog)

        right_layout.addWidget(self.connection_status)
        right_layout.addWidget(self.refresh_btn)
        right_layout.addWidget(self.settings_btn)

        layout.addWidget(left_section)
        layout.addStretch()
        layout.addWidget(right_section)

        return header

    def create_navigation_bar(self):
        """创建现代化导航栏"""
        nav_bar = QFrame()
        nav_bar.setFixedHeight(70)
        nav_bar.setStyleSheet(f"""
            QFrame {{
                background: {ModernStyleManager.get_color('surface')};
                border: none;
                border-bottom: 1px solid {ModernStyleManager.get_color('border')};
            }}
        """)

        layout = QHBoxLayout(nav_bar)
        layout.setContentsMargins(32, 12, 32, 12)
        layout.setSpacing(8)

        # 导航按钮
        self.nav_buttons = []
        nav_items = [
            ("📊", "仪表板", 0),
            ("👥", "邀请成员", 1),
            ("🔧", "团队管理", 2),
            ("⚡", "批量操作", 3),
            ("📈", "数据视图", 4),
            ("⚙️", "设置", 5)
        ]

        for icon, text, index in nav_items:
            btn = ModernStyleManager.create_navigation_button(text, icon, index == 0)
            btn.clicked.connect(lambda checked=False, idx=index: self.switch_page(idx))
            self.nav_buttons.append(btn)
            layout.addWidget(btn)

        layout.addStretch()
        return nav_bar

    def create_content_area(self):
        """创建主内容区域（支持垂直滚动）"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)

        # 创建堆叠窗口
        self.stacked_widget = QStackedWidget()

        # 创建各个页面
        self.create_pages()

        scroll_area.setWidget(self.stacked_widget)
        return scroll_area

    def create_pages(self):
        """创建所有功能页面"""
        # 仪表板页面
        dashboard_page = self.create_dashboard_page()
        self.stacked_widget.addWidget(dashboard_page)

        # 邀请成员页面
        invite_page = self.create_invite_page()
        self.stacked_widget.addWidget(invite_page)

        # 团队管理页面
        manage_page = self.create_manage_page()
        self.stacked_widget.addWidget(manage_page)

        # 批量操作页面
        batch_page = self.create_batch_page()
        self.stacked_widget.addWidget(batch_page)

        # 数据视图页面
        data_page = self.create_data_page()
        self.stacked_widget.addWidget(data_page)

        # 设置页面
        settings_page = self.create_settings_page()
        self.stacked_widget.addWidget(settings_page)

    def create_dashboard_page(self):
        """创建超美观仪表板页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(40, 32, 40, 32)
        layout.setSpacing(32)

        # 欢迎区域
        welcome_card = ModernStyleManager.create_card("", style="gradient")
        welcome_content = QWidget()
        welcome_layout = QVBoxLayout(welcome_content)
        welcome_layout.setSpacing(16)

        welcome_title = QLabel("欢迎回来！👋")
        welcome_title.setStyleSheet(f"""
            font-size: 32px;
            font-weight: 800;
            color: {ModernStyleManager.TEXT_PRIMARY};
            margin: 0;
            letter-spacing: -1px;
        """)

        welcome_subtitle = QLabel("管理您的团队，轻松高效")
        welcome_subtitle.setStyleSheet(f"""
            font-size: 16px;
            color: {ModernStyleManager.TEXT_SECONDARY};
            margin: 0;
            font-weight: 400;
        """)

        welcome_layout.addWidget(welcome_title)
        welcome_layout.addWidget(welcome_subtitle)
        welcome_card.layout().addWidget(welcome_content)
        layout.addWidget(welcome_card)

        # 统计卡片区域
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(24)

        # 创建精美统计卡片
        self.members_stat = ModernStyleManager.create_stat_card(
            "👥", "团队成员", "0", "当前活跃成员", ModernStyleManager.PRIMARY
        )
        self.invites_stat = ModernStyleManager.create_stat_card(
            "📧", "待处理邀请", "0", "等待接受的邀请", ModernStyleManager.INFO
        )
        self.credits_stat = ModernStyleManager.create_stat_card(
            "💎", "可用积分", "0", "当前账户余额", ModernStyleManager.SUCCESS
        )
        self.today_invites_stat = ModernStyleManager.create_stat_card(
            "📅", "今日邀请", "0", "今天发送的邀请", ModernStyleManager.WARNING
        )

        stats_layout.addWidget(self.members_stat)
        stats_layout.addWidget(self.invites_stat)
        stats_layout.addWidget(self.credits_stat)
        stats_layout.addWidget(self.today_invites_stat)

        layout.addLayout(stats_layout)

        # 快速操作区域
        quick_actions_card = ModernStyleManager.create_card("⚡ 快速操作", icon="🚀", style="primary")
        quick_actions_content = QWidget()
        quick_layout = QHBoxLayout(quick_actions_content)
        quick_layout.setSpacing(20)

        # 精美快速操作按钮
        invite_btn = ModernStyleManager.create_modern_button("邀请成员", "primary", "👥", "large")
        invite_btn.clicked.connect(lambda: self.switch_page(1))

        manage_btn = ModernStyleManager.create_modern_button("管理团队", "secondary", "🔧", "large")
        manage_btn.clicked.connect(lambda: self.switch_page(2))

        batch_btn = ModernStyleManager.create_modern_button("批量操作", "success", "⚡", "large")
        batch_btn.clicked.connect(lambda: self.switch_page(3))

        refresh_btn = ModernStyleManager.create_modern_button("刷新数据", "outline", "🔄", "large")
        refresh_btn.clicked.connect(self.refresh_team_data)

        quick_layout.addWidget(invite_btn)
        quick_layout.addWidget(manage_btn)
        quick_layout.addWidget(batch_btn)
        quick_layout.addWidget(refresh_btn)

        quick_actions_card.layout().addWidget(quick_actions_content)
        layout.addWidget(quick_actions_card)

        # 系统状态和最近活动
        bottom_layout = QHBoxLayout()
        bottom_layout.setSpacing(24)

        # 系统状态卡片
        status_card = ModernStyleManager.create_card("📊 系统状态", icon="💻", style="success")
        status_content = QWidget()
        status_layout = QVBoxLayout(status_content)
        status_layout.setSpacing(16)

        # 连接状态
        self.dashboard_connection_status = QLabel("🔴 未连接到服务器")
        self.dashboard_connection_status.setStyleSheet(f"""
            font-size: 15px;
            color: {ModernStyleManager.ERROR};
            padding: 12px 16px;
            background: {ModernStyleManager.ERROR}15;
            border: 1px solid {ModernStyleManager.ERROR}30;
            border-radius: 10px;
            font-weight: 500;
        """)

        # 最后更新时间
        self.last_update_label = QLabel("最后更新：从未")
        self.last_update_label.setStyleSheet(f"""
            font-size: 14px;
            color: {ModernStyleManager.TEXT_SECONDARY};
            margin: 0;
            font-weight: 400;
        """)

        status_layout.addWidget(self.dashboard_connection_status)
        status_layout.addWidget(self.last_update_label)
        status_card.layout().addWidget(status_content)

        # 最近活动卡片
        activity_card = ModernStyleManager.create_card("📈 最近活动", icon="⏰")
        activity_content = QWidget()
        activity_layout = QVBoxLayout(activity_content)

        activity_placeholder = QLabel("暂无最近活动")
        activity_placeholder.setStyleSheet(f"""
            color: {ModernStyleManager.TEXT_TERTIARY};
            font-style: italic;
            padding: 20px;
            text-align: center;
            font-size: 14px;
        """)
        activity_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)

        activity_layout.addWidget(activity_placeholder)
        activity_card.layout().addWidget(activity_content)

        bottom_layout.addWidget(status_card)
        bottom_layout.addWidget(activity_card)
        layout.addLayout(bottom_layout)

        layout.addStretch()
        return page

    def create_invite_page(self):
        """创建超美观邀请成员页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(40, 32, 40, 32)
        layout.setSpacing(32)

        # 单个邀请卡片
        invite_card = ModernStyleManager.create_card("📧 发送邀请", icon="✉️", style="primary")
        invite_content = QWidget()
        invite_layout = QVBoxLayout(invite_content)
        invite_layout.setSpacing(20)

        # 邮箱输入区域
        email_section = QWidget()
        email_section_layout = QVBoxLayout(email_section)
        email_section_layout.setSpacing(12)

        email_label = QLabel("邮箱地址")
        email_label.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 600;
            color: {ModernStyleManager.TEXT_PRIMARY};
            margin: 0;
        """)

        from PyQt6.QtWidgets import QLineEdit
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("请输入要邀请的邮箱地址，例如：<EMAIL>")
        self.email_input.setStyleSheet(f"""
            QLineEdit {{
                font-size: 15px;
                padding: 16px 20px;
                border-radius: 12px;
                border: 2px solid {ModernStyleManager.BORDER_LIGHT};
                background: {ModernStyleManager.SURFACE_PRIMARY};
            }}
            QLineEdit:focus {{
                border-color: {ModernStyleManager.PRIMARY};
            }}
        """)

        # 邮箱验证提示
        self.email_hint = QLabel("")
        self.email_hint.setStyleSheet(f"""
            font-size: 13px;
            color: {ModernStyleManager.TEXT_TERTIARY};
            margin: 0;
        """)
        self.email_input.textChanged.connect(self.validate_email_input)

        email_section_layout.addWidget(email_label)
        email_section_layout.addWidget(self.email_input)
        email_section_layout.addWidget(self.email_hint)

        # 发送按钮
        send_btn = ModernStyleManager.create_modern_button("发送邀请", "primary", "📤", "large")
        send_btn.clicked.connect(self.send_invitation)

        invite_layout.addWidget(email_section)
        invite_layout.addWidget(send_btn)

        invite_card.layout().addWidget(invite_content)
        layout.addWidget(invite_card)

        # 批量邀请卡片
        batch_invite_card = ModernStyleManager.create_card("📋 批量邀请", icon="📝", style="success")
        batch_content = QWidget()
        batch_layout = QVBoxLayout(batch_content)
        batch_layout.setSpacing(20)

        # 批量邮箱输入区域
        batch_section = QWidget()
        batch_section_layout = QVBoxLayout(batch_section)
        batch_section_layout.setSpacing(12)

        batch_label = QLabel("批量邮箱地址")
        batch_label.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 600;
            color: {ModernStyleManager.TEXT_PRIMARY};
            margin: 0;
        """)

        batch_hint = QLabel("每行输入一个邮箱地址，支持批量处理")
        batch_hint.setStyleSheet(f"""
            font-size: 13px;
            color: {ModernStyleManager.TEXT_SECONDARY};
            margin: 0;
        """)

        from PyQt6.QtWidgets import QTextEdit
        self.batch_email_input = QTextEdit()
        self.batch_email_input.setPlaceholderText("""请输入多个邮箱地址，每行一个，例如：
<EMAIL>
<EMAIL>
<EMAIL>""")
        self.batch_email_input.setMaximumHeight(150)
        self.batch_email_input.setStyleSheet(f"""
            QTextEdit {{
                font-size: 14px;
                padding: 16px 20px;
                border-radius: 12px;
                border: 2px solid {ModernStyleManager.BORDER_LIGHT};
                background: {ModernStyleManager.SURFACE_PRIMARY};
                font-family: 'Consolas', 'Courier New', monospace;
            }}
            QTextEdit:focus {{
                border-color: {ModernStyleManager.SUCCESS};
            }}
        """)

        # 批量邮箱统计
        self.batch_count_label = QLabel("邮箱数量：0")
        self.batch_count_label.setStyleSheet(f"""
            font-size: 13px;
            color: {ModernStyleManager.TEXT_TERTIARY};
            margin: 0;
        """)
        self.batch_email_input.textChanged.connect(self.update_batch_count)

        batch_section_layout.addWidget(batch_label)
        batch_section_layout.addWidget(batch_hint)
        batch_section_layout.addWidget(self.batch_email_input)
        batch_section_layout.addWidget(self.batch_count_label)

        # 批量操作按钮
        batch_buttons_layout = QHBoxLayout()
        batch_buttons_layout.setSpacing(16)

        validate_btn = ModernStyleManager.create_modern_button("验证邮箱", "outline", "✅", "medium")
        validate_btn.clicked.connect(self.validate_batch_emails)

        batch_send_btn = ModernStyleManager.create_modern_button("批量发送", "success", "📬", "large")
        batch_send_btn.clicked.connect(self.send_batch_invitations)

        clear_btn = ModernStyleManager.create_modern_button("清空", "ghost", "🗑️", "medium")
        clear_btn.clicked.connect(self.clear_batch_emails)

        batch_buttons_layout.addWidget(validate_btn)
        batch_buttons_layout.addWidget(batch_send_btn)
        batch_buttons_layout.addWidget(clear_btn)
        batch_buttons_layout.addStretch()

        batch_layout.addWidget(batch_section)
        batch_layout.addLayout(batch_buttons_layout)

        batch_invite_card.layout().addWidget(batch_content)
        layout.addWidget(batch_invite_card)

        # 邀请历史卡片
        history_card = ModernStyleManager.create_card("📈 邀请统计", icon="📊")
        history_content = QWidget()
        history_layout = QVBoxLayout(history_content)

        # 统计信息
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(24)

        self.today_invites_mini = self.create_mini_stat("📅", "今日邀请", "0")
        self.total_invites_mini = self.create_mini_stat("📧", "总邀请数", "0")
        self.success_rate_mini = self.create_mini_stat("✅", "成功率", "0%")

        stats_layout.addWidget(self.today_invites_mini)
        stats_layout.addWidget(self.total_invites_mini)
        stats_layout.addWidget(self.success_rate_mini)
        stats_layout.addStretch()

        history_layout.addLayout(stats_layout)
        history_card.layout().addWidget(history_content)
        layout.addWidget(history_card)

        layout.addStretch()
        return page

    def create_mini_stat(self, icon, title, value):
        """创建小型统计组件"""
        stat_widget = QWidget()
        stat_widget.setFixedHeight(80)
        stat_widget.setStyleSheet(f"""
            QWidget {{
                background: {ModernStyleManager.BG_SECONDARY};
                border: 1px solid {ModernStyleManager.BORDER_LIGHT};
                border-radius: 12px;
                padding: 12px;
            }}
        """)

        layout = QVBoxLayout(stat_widget)
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(4)

        # 图标和数值
        top_layout = QHBoxLayout()
        top_layout.setSpacing(8)

        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 18px;")

        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            font-size: 20px;
            font-weight: 700;
            color: {ModernStyleManager.PRIMARY};
        """)

        top_layout.addWidget(icon_label)
        top_layout.addWidget(value_label)
        top_layout.addStretch()

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            font-size: 12px;
            color: {ModernStyleManager.TEXT_SECONDARY};
            font-weight: 500;
        """)

        layout.addLayout(top_layout)
        layout.addWidget(title_label)

        # 存储值标签以便更新
        stat_widget.value_label = value_label
        return stat_widget

    def create_manage_page(self):
        """创建超美观团队管理页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(40, 32, 40, 32)
        layout.setSpacing(32)

        # 团队概览卡片
        overview_card = ModernStyleManager.create_card("👥 团队概览", icon="📊", style="primary")
        overview_content = QWidget()
        overview_layout = QHBoxLayout(overview_content)
        overview_layout.setSpacing(24)

        # 团队统计
        self.team_members_stat = ModernStyleManager.create_stat_card(
            "👥", "团队成员", "0", "当前成员数量", ModernStyleManager.PRIMARY
        )
        self.pending_invites_stat = ModernStyleManager.create_stat_card(
            "⏳", "待处理邀请", "0", "等待接受的邀请", ModernStyleManager.WARNING
        )
        self.active_members_stat = ModernStyleManager.create_stat_card(
            "✅", "活跃成员", "0", "最近活跃的成员", ModernStyleManager.SUCCESS
        )

        overview_layout.addWidget(self.team_members_stat)
        overview_layout.addWidget(self.pending_invites_stat)
        overview_layout.addWidget(self.active_members_stat)

        overview_card.layout().addWidget(overview_content)
        layout.addWidget(overview_card)

        # 团队成员列表卡片
        members_card = ModernStyleManager.create_card("👨‍👩‍👧‍👦 团队成员列表", icon="📋")
        members_content = QWidget()
        members_layout = QVBoxLayout(members_content)
        members_layout.setSpacing(20)

        # 操作按钮区域
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(16)

        refresh_members_btn = ModernStyleManager.create_modern_button("刷新列表", "primary", "🔄")
        refresh_members_btn.clicked.connect(self.refresh_team_members)

        export_btn = ModernStyleManager.create_modern_button("导出成员", "outline", "📤")
        export_btn.clicked.connect(self.export_team_members)

        bulk_delete_btn = ModernStyleManager.create_modern_button("批量删除", "ghost", "🗑️")
        bulk_delete_btn.clicked.connect(self.bulk_delete_members)

        actions_layout.addWidget(refresh_members_btn)
        actions_layout.addWidget(export_btn)
        actions_layout.addWidget(bulk_delete_btn)
        actions_layout.addStretch()

        members_layout.addLayout(actions_layout)

        # 成员列表表格
        from PyQt6.QtWidgets import QTableWidget, QTableWidgetItem, QHeaderView
        self.members_table = QTableWidget()
        self.members_table.setColumnCount(5)
        self.members_table.setHorizontalHeaderLabels(["邮箱", "状态", "加入时间", "最后活跃", "操作"])

        # 设置表格样式
        self.members_table.setStyleSheet(f"""
            QTableWidget {{
                background: {ModernStyleManager.SURFACE_PRIMARY};
                border: 1px solid {ModernStyleManager.BORDER_LIGHT};
                border-radius: 12px;
                gridline-color: {ModernStyleManager.BORDER_LIGHT};
                font-size: 14px;
            }}
            QTableWidget::item {{
                padding: 12px;
                border-bottom: 1px solid {ModernStyleManager.BORDER_LIGHT};
            }}
            QHeaderView::section {{
                background: {ModernStyleManager.PRIMARY};
                color: white;
                padding: 12px;
                border: none;
                font-weight: 600;
                font-size: 13px;
            }}
        """)

        # 设置表格属性
        self.members_table.setAlternatingRowColors(True)
        self.members_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.members_table.horizontalHeader().setStretchLastSection(True)
        self.members_table.setMinimumHeight(300)

        members_layout.addWidget(self.members_table)

        # 加载状态提示
        self.members_loading_label = QLabel("点击'刷新列表'加载团队成员数据")
        self.members_loading_label.setStyleSheet(f"""
            color: {ModernStyleManager.TEXT_TERTIARY};
            font-style: italic;
            padding: 20px;
            text-align: center;
            font-size: 14px;
        """)
        self.members_loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        members_layout.addWidget(self.members_loading_label)

        members_card.layout().addWidget(members_content)
        layout.addWidget(members_card)

        # 待处理邀请卡片
        pending_card = ModernStyleManager.create_card("⏳ 待处理邀请", icon="📧", style="warning")
        pending_content = QWidget()
        pending_layout = QVBoxLayout(pending_content)
        pending_layout.setSpacing(20)

        # 待处理邀请操作
        pending_actions_layout = QHBoxLayout()
        pending_actions_layout.setSpacing(16)

        refresh_pending_btn = ModernStyleManager.create_modern_button("刷新邀请", "warning", "🔄")
        refresh_pending_btn.clicked.connect(self.refresh_pending_invites)

        export_pending_btn = ModernStyleManager.create_modern_button("导出邮箱", "outline", "📋")
        export_pending_btn.clicked.connect(self.export_pending_emails)

        cancel_all_btn = ModernStyleManager.create_modern_button("取消全部", "ghost", "❌")
        cancel_all_btn.clicked.connect(self.cancel_all_invites)

        pending_actions_layout.addWidget(refresh_pending_btn)
        pending_actions_layout.addWidget(export_pending_btn)
        pending_actions_layout.addWidget(cancel_all_btn)
        pending_actions_layout.addStretch()

        pending_layout.addLayout(pending_actions_layout)

        # 待处理邀请列表
        self.pending_table = QTableWidget()
        self.pending_table.setColumnCount(4)
        self.pending_table.setHorizontalHeaderLabels(["邮箱", "邀请时间", "状态", "操作"])
        self.pending_table.setStyleSheet(self.members_table.styleSheet())
        self.pending_table.setAlternatingRowColors(True)
        self.pending_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.pending_table.horizontalHeader().setStretchLastSection(True)
        self.pending_table.setMinimumHeight(200)

        pending_layout.addWidget(self.pending_table)

        # 待处理邀请加载状态
        self.pending_loading_label = QLabel("点击'刷新邀请'加载待处理邀请数据")
        self.pending_loading_label.setStyleSheet(f"""
            color: {ModernStyleManager.TEXT_TERTIARY};
            font-style: italic;
            padding: 20px;
            text-align: center;
            font-size: 14px;
        """)
        self.pending_loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        pending_layout.addWidget(self.pending_loading_label)

        pending_card.layout().addWidget(pending_content)
        layout.addWidget(pending_card)

        layout.addStretch()
        return page

    def create_batch_page(self):
        """创建超美观批量操作页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(40, 32, 40, 32)
        layout.setSpacing(32)

        # 计划切换卡片
        plan_card = ModernStyleManager.create_card("🎯 计划管理", icon="⚙️", style="primary")
        plan_content = QWidget()
        plan_layout = QVBoxLayout(plan_content)
        plan_layout.setSpacing(24)

        # 计划说明
        plan_desc = QLabel("快速切换账户计划类型")
        plan_desc.setStyleSheet(f"""
            font-size: 16px;
            color: {ModernStyleManager.TEXT_SECONDARY};
            margin: 0;
        """)

        # 计划切换按钮
        plan_buttons_layout = QHBoxLayout()
        plan_buttons_layout.setSpacing(20)

        community_btn = ModernStyleManager.create_modern_button("切换到 Community Plan", "success", "🌱", "large")
        community_btn.clicked.connect(self.switch_to_community_plan)

        max_btn = ModernStyleManager.create_modern_button("切换到 Max Plan", "secondary", "🚀", "large")
        max_btn.clicked.connect(self.switch_to_max_plan)

        plan_buttons_layout.addWidget(community_btn)
        plan_buttons_layout.addWidget(max_btn)
        plan_buttons_layout.addStretch()

        plan_layout.addWidget(plan_desc)
        plan_layout.addLayout(plan_buttons_layout)
        plan_card.layout().addWidget(plan_content)
        layout.addWidget(plan_card)

        # 批量邀请管理卡片
        batch_invite_card = ModernStyleManager.create_card("📬 批量邀请管理", icon="📋", style="success")
        batch_invite_content = QWidget()
        batch_invite_layout = QVBoxLayout(batch_invite_content)
        batch_invite_layout.setSpacing(20)

        # 批量邀请文件上传区域
        upload_section = QWidget()
        upload_layout = QVBoxLayout(upload_section)
        upload_layout.setSpacing(12)

        upload_label = QLabel("从文件导入邮箱地址")
        upload_label.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 600;
            color: {ModernStyleManager.TEXT_PRIMARY};
        """)

        upload_hint = QLabel("支持 .txt, .csv 文件，每行一个邮箱地址")
        upload_hint.setStyleSheet(f"""
            font-size: 13px;
            color: {ModernStyleManager.TEXT_SECONDARY};
        """)

        upload_buttons_layout = QHBoxLayout()
        upload_buttons_layout.setSpacing(16)

        select_file_btn = ModernStyleManager.create_modern_button("选择文件", "outline", "📁")
        select_file_btn.clicked.connect(self.select_email_file)

        import_btn = ModernStyleManager.create_modern_button("导入邮箱", "success", "📥")
        import_btn.clicked.connect(self.import_emails_from_file)

        upload_buttons_layout.addWidget(select_file_btn)
        upload_buttons_layout.addWidget(import_btn)
        upload_buttons_layout.addStretch()

        upload_layout.addWidget(upload_label)
        upload_layout.addWidget(upload_hint)
        upload_layout.addLayout(upload_buttons_layout)

        batch_invite_layout.addWidget(upload_section)
        batch_invite_card.layout().addWidget(batch_invite_content)
        layout.addWidget(batch_invite_card)

        # 批量成员管理卡片
        batch_manage_card = ModernStyleManager.create_card("👥 批量成员管理", icon="🔧", style="warning")
        batch_manage_content = QWidget()
        batch_manage_layout = QVBoxLayout(batch_manage_content)
        batch_manage_layout.setSpacing(20)

        # 批量操作说明
        manage_desc = QLabel("对团队成员进行批量操作")
        manage_desc.setStyleSheet(f"""
            font-size: 16px;
            color: {ModernStyleManager.TEXT_SECONDARY};
            margin: 0;
        """)

        # 批量操作按钮
        manage_buttons_layout = QHBoxLayout()
        manage_buttons_layout.setSpacing(20)

        export_all_btn = ModernStyleManager.create_modern_button("导出所有成员", "info", "📤", "large")
        export_all_btn.clicked.connect(self.export_all_members)

        cleanup_btn = ModernStyleManager.create_modern_button("清理无效邀请", "warning", "🧹", "large")
        cleanup_btn.clicked.connect(self.cleanup_invalid_invites)

        manage_buttons_layout.addWidget(export_all_btn)
        manage_buttons_layout.addWidget(cleanup_btn)
        manage_buttons_layout.addStretch()

        batch_manage_layout.addWidget(manage_desc)
        batch_manage_layout.addLayout(manage_buttons_layout)
        batch_manage_card.layout().addWidget(batch_manage_content)
        layout.addWidget(batch_manage_card)

        # 批量操作历史卡片
        history_card = ModernStyleManager.create_card("📊 操作历史", icon="📈")
        history_content = QWidget()
        history_layout = QVBoxLayout(history_content)

        # 操作统计
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(24)

        self.batch_invites_stat = self.create_mini_stat("📧", "批量邀请", "0")
        self.plan_switches_stat = self.create_mini_stat("🔄", "计划切换", "0")
        self.exports_stat = self.create_mini_stat("📤", "数据导出", "0")

        stats_layout.addWidget(self.batch_invites_stat)
        stats_layout.addWidget(self.plan_switches_stat)
        stats_layout.addWidget(self.exports_stat)
        stats_layout.addStretch()

        history_layout.addLayout(stats_layout)
        history_card.layout().addWidget(history_content)
        layout.addWidget(history_card)

        layout.addStretch()
        return page

    def create_data_page(self):
        """创建超美观数据视图页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(40, 32, 40, 32)
        layout.setSpacing(32)

        # 数据概览卡片
        overview_card = ModernStyleManager.create_card("📊 数据概览", icon="📈", style="gradient")
        overview_content = QWidget()
        overview_layout = QVBoxLayout(overview_content)
        overview_layout.setSpacing(24)

        # 关键指标
        metrics_layout = QHBoxLayout()
        metrics_layout.setSpacing(24)

        self.total_members_metric = ModernStyleManager.create_stat_card(
            "👥", "总成员数", "0", "包括活跃和待处理", ModernStyleManager.PRIMARY
        )
        self.growth_rate_metric = ModernStyleManager.create_stat_card(
            "📈", "增长率", "0%", "本月成员增长", ModernStyleManager.SUCCESS
        )
        self.active_rate_metric = ModernStyleManager.create_stat_card(
            "⚡", "活跃率", "0%", "活跃成员比例", ModernStyleManager.INFO
        )
        self.invite_success_metric = ModernStyleManager.create_stat_card(
            "✅", "邀请成功率", "0%", "邀请接受比例", ModernStyleManager.WARNING
        )

        metrics_layout.addWidget(self.total_members_metric)
        metrics_layout.addWidget(self.growth_rate_metric)
        metrics_layout.addWidget(self.active_rate_metric)
        metrics_layout.addWidget(self.invite_success_metric)

        overview_layout.addLayout(metrics_layout)
        overview_card.layout().addWidget(overview_content)
        layout.addWidget(overview_card)

        # 数据分析卡片
        analysis_layout = QHBoxLayout()
        analysis_layout.setSpacing(24)

        # 成员状态分布
        status_card = ModernStyleManager.create_card("👥 成员状态分布", icon="📊", style="primary")
        status_content = QWidget()
        status_content_layout = QVBoxLayout(status_content)
        status_content_layout.setSpacing(16)

        # 状态统计
        self.active_members_count = self.create_status_item("✅ 活跃成员", "0", ModernStyleManager.SUCCESS)
        self.pending_members_count = self.create_status_item("⏳ 待处理", "0", ModernStyleManager.WARNING)
        self.inactive_members_count = self.create_status_item("😴 非活跃", "0", ModernStyleManager.ERROR)

        status_content_layout.addWidget(self.active_members_count)
        status_content_layout.addWidget(self.pending_members_count)
        status_content_layout.addWidget(self.inactive_members_count)

        status_card.layout().addWidget(status_content)

        # 邀请趋势分析
        trend_card = ModernStyleManager.create_card("📈 邀请趋势", icon="📅", style="success")
        trend_content = QWidget()
        trend_content_layout = QVBoxLayout(trend_content)
        trend_content_layout.setSpacing(16)

        # 时间段统计
        self.today_invites_count = self.create_status_item("📅 今日邀请", "0", ModernStyleManager.PRIMARY)
        self.week_invites_count = self.create_status_item("📊 本周邀请", "0", ModernStyleManager.INFO)
        self.month_invites_count = self.create_status_item("📈 本月邀请", "0", ModernStyleManager.SUCCESS)

        trend_content_layout.addWidget(self.today_invites_count)
        trend_content_layout.addWidget(self.week_invites_count)
        trend_content_layout.addWidget(self.month_invites_count)

        trend_card.layout().addWidget(trend_content)

        analysis_layout.addWidget(status_card)
        analysis_layout.addWidget(trend_card)
        layout.addLayout(analysis_layout)

        # 详细数据表格卡片
        table_card = ModernStyleManager.create_card("📋 详细数据", icon="📊")
        table_content = QWidget()
        table_layout = QVBoxLayout(table_content)
        table_layout.setSpacing(20)

        # 数据操作按钮
        data_actions_layout = QHBoxLayout()
        data_actions_layout.setSpacing(16)

        refresh_data_btn = ModernStyleManager.create_modern_button("刷新数据", "primary", "🔄")
        refresh_data_btn.clicked.connect(self.refresh_data_analysis)

        export_report_btn = ModernStyleManager.create_modern_button("导出报告", "outline", "📤")
        export_report_btn.clicked.connect(self.export_data_report)

        analyze_btn = ModernStyleManager.create_modern_button("深度分析", "secondary", "🔍")
        analyze_btn.clicked.connect(self.perform_deep_analysis)

        data_actions_layout.addWidget(refresh_data_btn)
        data_actions_layout.addWidget(export_report_btn)
        data_actions_layout.addWidget(analyze_btn)
        data_actions_layout.addStretch()

        table_layout.addLayout(data_actions_layout)

        # 数据摘要表格
        self.data_summary_table = QTableWidget()
        self.data_summary_table.setColumnCount(3)
        self.data_summary_table.setHorizontalHeaderLabels(["指标", "数值", "说明"])
        self.data_summary_table.setStyleSheet(f"""
            QTableWidget {{
                background: {ModernStyleManager.SURFACE_PRIMARY};
                border: 1px solid {ModernStyleManager.BORDER_LIGHT};
                border-radius: 12px;
                gridline-color: {ModernStyleManager.BORDER_LIGHT};
                font-size: 14px;
            }}
            QTableWidget::item {{
                padding: 12px;
                border-bottom: 1px solid {ModernStyleManager.BORDER_LIGHT};
            }}
            QHeaderView::section {{
                background: {ModernStyleManager.INFO};
                color: white;
                padding: 12px;
                border: none;
                font-weight: 600;
                font-size: 13px;
            }}
        """)
        self.data_summary_table.setAlternatingRowColors(True)
        self.data_summary_table.horizontalHeader().setStretchLastSection(True)
        self.data_summary_table.setMinimumHeight(200)

        table_layout.addWidget(self.data_summary_table)

        # 数据加载提示
        self.data_loading_label = QLabel("点击'刷新数据'加载最新分析数据")
        self.data_loading_label.setStyleSheet(f"""
            color: {ModernStyleManager.TEXT_TERTIARY};
            font-style: italic;
            padding: 20px;
            text-align: center;
            font-size: 14px;
        """)
        self.data_loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        table_layout.addWidget(self.data_loading_label)

        table_card.layout().addWidget(table_content)
        layout.addWidget(table_card)

        layout.addStretch()
        return page

    def create_status_item(self, title, value, color):
        """创建状态项目"""
        item_widget = QWidget()
        item_widget.setFixedHeight(50)
        item_widget.setStyleSheet(f"""
            QWidget {{
                background: {color}10;
                border: 1px solid {color}30;
                border-radius: 8px;
                padding: 8px 16px;
            }}
        """)

        layout = QHBoxLayout(item_widget)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(12)

        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            font-size: 14px;
            font-weight: 500;
            color: {ModernStyleManager.TEXT_PRIMARY};
        """)

        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            font-size: 18px;
            font-weight: 700;
            color: {color};
        """)

        layout.addWidget(title_label)
        layout.addStretch()
        layout.addWidget(value_label)

        # 存储值标签以便更新
        item_widget.value_label = value_label
        return item_widget

    def create_settings_page(self):
        """创建超美观设置页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(40, 32, 40, 32)
        layout.setSpacing(32)

        # 应用设置卡片
        app_settings_card = ModernStyleManager.create_card("🔧 应用设置", icon="⚙️", style="primary")
        app_settings_content = QWidget()
        app_settings_layout = QVBoxLayout(app_settings_content)
        app_settings_layout.setSpacing(24)

        # 主题设置
        theme_section = QWidget()
        theme_layout = QVBoxLayout(theme_section)
        theme_layout.setSpacing(12)

        theme_title = QLabel("🎨 主题设置")
        theme_title.setStyleSheet(f"""
            font-size: 18px;
            font-weight: 600;
            color: {ModernStyleManager.TEXT_PRIMARY};
            margin: 0;
        """)

        theme_buttons_layout = QHBoxLayout()
        theme_buttons_layout.setSpacing(16)

        light_theme_btn = ModernStyleManager.create_modern_button("浅色主题", "outline", "☀️")
        light_theme_btn.clicked.connect(lambda: self.set_theme("light"))

        dark_theme_btn = ModernStyleManager.create_modern_button("深色主题", "outline", "🌙")
        dark_theme_btn.clicked.connect(lambda: self.set_theme("dark"))

        auto_theme_btn = ModernStyleManager.create_modern_button("自动切换", "ghost", "🔄")
        auto_theme_btn.clicked.connect(self.toggle_theme)

        theme_buttons_layout.addWidget(light_theme_btn)
        theme_buttons_layout.addWidget(dark_theme_btn)
        theme_buttons_layout.addWidget(auto_theme_btn)
        theme_buttons_layout.addStretch()

        theme_layout.addWidget(theme_title)
        theme_layout.addLayout(theme_buttons_layout)

        # 应用配置
        config_section = QWidget()
        config_layout = QVBoxLayout(config_section)
        config_layout.setSpacing(12)

        config_title = QLabel("🔧 应用配置")
        config_title.setStyleSheet(f"""
            font-size: 18px;
            font-weight: 600;
            color: {ModernStyleManager.TEXT_PRIMARY};
            margin: 0;
        """)

        config_buttons_layout = QHBoxLayout()
        config_buttons_layout.setSpacing(16)

        open_config_btn = ModernStyleManager.create_modern_button("打开配置", "primary", "📝", "large")
        open_config_btn.clicked.connect(self.open_config_dialog)

        reset_config_btn = ModernStyleManager.create_modern_button("重置配置", "outline", "🔄")
        reset_config_btn.clicked.connect(self.reset_config)

        config_buttons_layout.addWidget(open_config_btn)
        config_buttons_layout.addWidget(reset_config_btn)
        config_buttons_layout.addStretch()

        config_layout.addWidget(config_title)
        config_layout.addLayout(config_buttons_layout)

        app_settings_layout.addWidget(theme_section)
        app_settings_layout.addWidget(config_section)
        app_settings_card.layout().addWidget(app_settings_content)
        layout.addWidget(app_settings_card)

        # 数据管理卡片
        data_card = ModernStyleManager.create_card("💾 数据管理", icon="🗄️", style="success")
        data_content = QWidget()
        data_layout = QVBoxLayout(data_content)
        data_layout.setSpacing(20)

        data_desc = QLabel("管理应用数据和缓存")
        data_desc.setStyleSheet(f"""
            font-size: 16px;
            color: {ModernStyleManager.TEXT_SECONDARY};
            margin: 0;
        """)

        data_buttons_layout = QHBoxLayout()
        data_buttons_layout.setSpacing(16)

        clear_cache_btn = ModernStyleManager.create_modern_button("清除缓存", "warning", "🧹")
        clear_cache_btn.clicked.connect(self.clear_cache)

        export_data_btn = ModernStyleManager.create_modern_button("导出数据", "info", "📤")
        export_data_btn.clicked.connect(self.export_all_data)

        backup_btn = ModernStyleManager.create_modern_button("备份设置", "outline", "💾")
        backup_btn.clicked.connect(self.backup_settings)

        data_buttons_layout.addWidget(clear_cache_btn)
        data_buttons_layout.addWidget(export_data_btn)
        data_buttons_layout.addWidget(backup_btn)
        data_buttons_layout.addStretch()

        data_layout.addWidget(data_desc)
        data_layout.addLayout(data_buttons_layout)
        data_card.layout().addWidget(data_content)
        layout.addWidget(data_card)

        # 关于应用卡片
        about_card = ModernStyleManager.create_card("ℹ️ 关于应用", icon="📱")
        about_content = QWidget()
        about_layout = QVBoxLayout(about_content)
        about_layout.setSpacing(16)

        # 应用信息
        app_info_layout = QVBoxLayout()
        app_info_layout.setSpacing(8)

        app_name = QLabel("🛠️ 团队管理工具")
        app_name.setStyleSheet(f"""
            font-size: 20px;
            font-weight: 700;
            color: {ModernStyleManager.TEXT_PRIMARY};
            margin: 0;
        """)

        app_version = QLabel("版本：v1.0.0 - 现代化版本")
        app_version.setStyleSheet(f"""
            font-size: 14px;
            color: {ModernStyleManager.TEXT_SECONDARY};
            margin: 0;
        """)

        app_desc = QLabel("一个现代化的团队管理工具，支持成员邀请、数据分析等功能")
        app_desc.setStyleSheet(f"""
            font-size: 14px;
            color: {ModernStyleManager.TEXT_TERTIARY};
            margin: 0;
        """)

        app_info_layout.addWidget(app_name)
        app_info_layout.addWidget(app_version)
        app_info_layout.addWidget(app_desc)

        # 操作按钮
        about_buttons_layout = QHBoxLayout()
        about_buttons_layout.setSpacing(16)

        check_update_btn = ModernStyleManager.create_modern_button("检查更新", "outline", "🔄")
        check_update_btn.clicked.connect(self.check_for_updates)

        help_btn = ModernStyleManager.create_modern_button("帮助文档", "ghost", "❓")
        help_btn.clicked.connect(self.show_help)

        about_buttons_layout.addWidget(check_update_btn)
        about_buttons_layout.addWidget(help_btn)
        about_buttons_layout.addStretch()

        about_layout.addLayout(app_info_layout)
        about_layout.addLayout(about_buttons_layout)
        about_card.layout().addWidget(about_content)
        layout.addWidget(about_card)

        layout.addStretch()
        return page

    def create_footer(self):
        """创建底部状态栏"""
        footer = QFrame()
        footer.setFixedHeight(40)
        footer.setStyleSheet(f"""
            QFrame {{
                background: {ModernStyleManager.get_color('bg_secondary')};
                border: none;
                border-top: 1px solid {ModernStyleManager.get_color('border')};
            }}
        """)

        layout = QHBoxLayout(footer)
        layout.setContentsMargins(32, 8, 32, 8)

        # 状态信息
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet(f"""
            color: {ModernStyleManager.get_color('text_secondary')};
            font-size: 12px;
        """)

        # 版本信息
        version_label = QLabel("v1.0 - 现代化版本")
        version_label.setStyleSheet(f"""
            color: {ModernStyleManager.get_color('text_tertiary')};
            font-size: 11px;
        """)

        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(version_label)

        return footer

    def switch_page(self, index):
        """切换页面"""
        if index == self.current_page:
            return

        # 更新侧边栏导航按钮状态
        nav_items = [
            ("📊", "仪表板"),
            ("👥", "邀请成员"),
            ("🔧", "团队管理"),
            ("⚡", "批量操作"),
            ("📈", "数据视图"),
            ("⚙️", "设置")
        ]

        for i, btn in enumerate(self.nav_buttons):
            is_active = (i == index)
            icon, text = nav_items[i]
            btn.setStyleSheet(self.create_sidebar_nav_button(icon, text, is_active).styleSheet())

        # 切换页面
        self.stacked_widget.setCurrentIndex(index)
        self.current_page = index

        # 更新顶部页面标题
        page_titles = [
            "📊 仪表板",
            "👥 邀请成员",
            "🔧 团队管理",
            "⚡ 批量操作",
            "📈 数据视图",
            "⚙️ 设置"
        ]
        if index < len(page_titles):
            self.page_title.setText(page_titles[index])
            self.top_status_label.setText(f"当前页面：{page_titles[index].split(' ')[1]}")

    def init_status_bar(self):
        """初始化状态栏（兼容性方法）"""
        pass  # 状态栏已在footer中实现

    def toggle_theme(self):
        """切换主题"""
        current_theme = ModernStyleManager.current_theme
        new_theme = "dark" if current_theme == "light" else "light"
        ModernStyleManager.switch_theme(new_theme)

        # 重新应用样式
        app = QApplication.instance()
        app.setStyleSheet(ModernStyleManager.get_app_style())

        self.status_label.setText(f"已切换到{'深色' if new_theme == 'dark' else '浅色'}主题")

    def open_config_dialog(self):
        """打开配置对话框"""
        dialog = ConfigDialog(self.config, self)
        dialog.config_changed.connect(self.apply_config)
        dialog.show()

    def apply_config(self):
        """应用新的配置设置"""
        print("正在应用新的配置设置...")
        self.api_client = APIClient(self.config)
        self.status_label.setText("配置已更新")

    def refresh_team_data(self):
        """刷新团队数据"""
        self.status_label.setText("正在刷新数据...")
        # 这里将调用实际的数据刷新逻辑
        QTimer.singleShot(1000, lambda: self.status_label.setText("数据刷新完成"))

    def validate_email_input(self):
        """验证单个邮箱输入"""
        email = self.email_input.text().strip()
        if not email:
            self.email_hint.setText("")
            return

        if self.api_client.validate_email(email):
            self.email_hint.setText("✅ 邮箱格式正确")
            self.email_hint.setStyleSheet(f"color: {ModernStyleManager.SUCCESS}; font-size: 13px;")
        else:
            self.email_hint.setText("❌ 邮箱格式不正确")
            self.email_hint.setStyleSheet(f"color: {ModernStyleManager.ERROR}; font-size: 13px;")

    def update_batch_count(self):
        """更新批量邮箱数量"""
        emails_text = self.batch_email_input.toPlainText().strip()
        if not emails_text:
            self.batch_count_label.setText("邮箱数量：0")
            return

        emails = [email.strip() for email in emails_text.split('\n') if email.strip()]
        valid_count = sum(1 for email in emails if self.api_client.validate_email(email))
        total_count = len(emails)

        self.batch_count_label.setText(f"邮箱数量：{total_count} (有效：{valid_count})")

        if valid_count == total_count and total_count > 0:
            self.batch_count_label.setStyleSheet(f"color: {ModernStyleManager.SUCCESS}; font-size: 13px;")
        elif valid_count > 0:
            self.batch_count_label.setStyleSheet(f"color: {ModernStyleManager.WARNING}; font-size: 13px;")
        else:
            self.batch_count_label.setStyleSheet(f"color: {ModernStyleManager.ERROR}; font-size: 13px;")

    def validate_batch_emails(self):
        """验证批量邮箱"""
        emails_text = self.batch_email_input.toPlainText().strip()
        if not emails_text:
            self.show_message("请先输入邮箱地址", "warning")
            return

        emails = [email.strip() for email in emails_text.split('\n') if email.strip()]
        valid_emails = []
        invalid_emails = []

        for email in emails:
            if self.api_client.validate_email(email):
                valid_emails.append(email)
            else:
                invalid_emails.append(email)

        message = f"验证完成！\n有效邮箱：{len(valid_emails)} 个\n无效邮箱：{len(invalid_emails)} 个"
        if invalid_emails:
            message += f"\n\n无效邮箱列表：\n" + "\n".join(invalid_emails[:5])
            if len(invalid_emails) > 5:
                message += f"\n... 还有 {len(invalid_emails) - 5} 个"

        self.show_message(message, "info")

    def clear_batch_emails(self):
        """清空批量邮箱输入"""
        self.batch_email_input.clear()
        self.update_batch_count()

    def send_invitation(self):
        """发送单个邀请"""
        email = self.email_input.text().strip()
        if not email:
            self.show_message("请输入邮箱地址", "warning")
            return

        if not self.api_client.validate_email(email):
            self.show_message("邮箱格式不正确", "error")
            return

        # 显示加载状态
        self.show_loading("正在发送邀请...")

        # 使用线程发送邀请
        self.worker_thread = WorkerThread(
            target=self.api_client.invite_members,
            args=([email],)
        )
        self.worker_thread.finished.connect(lambda success, result: self.on_invitation_sent(success, result, email))
        self.worker_thread.start()

    def send_batch_invitations(self):
        """发送批量邀请"""
        emails_text = self.batch_email_input.toPlainText().strip()
        if not emails_text:
            self.show_message("请输入邮箱地址", "warning")
            return

        emails = [email.strip() for email in emails_text.split('\n') if email.strip()]
        if not emails:
            self.show_message("没有找到有效的邮箱地址", "warning")
            return

        # 验证所有邮箱
        valid_emails = [email for email in emails if self.api_client.validate_email(email)]
        invalid_emails = [email for email in emails if not self.api_client.validate_email(email)]

        if not valid_emails:
            self.show_message("没有有效的邮箱地址", "error")
            return

        if invalid_emails:
            message = f"发现 {len(invalid_emails)} 个无效邮箱，是否继续发送 {len(valid_emails)} 个有效邮箱的邀请？"
            # 这里应该显示确认对话框，暂时直接继续
            pass

        # 显示加载状态
        self.show_loading(f"正在批量发送 {len(valid_emails)} 个邀请...")

        # 使用线程发送批量邀请
        self.worker_thread = WorkerThread(
            target=self.api_client.invite_members,
            args=(valid_emails,)
        )
        self.worker_thread.finished.connect(lambda success, result: self.on_batch_invitation_sent(success, result, valid_emails))
        self.worker_thread.start()

    def on_invitation_sent(self, success, result, email):
        """单个邀请发送完成回调"""
        self.hide_loading()

        if success:
            self.show_message(f"邀请已成功发送到 {email}", "success")
            self.email_input.clear()
            self.email_hint.setText("")
            # 更新统计
            self.update_invitation_stats()
        else:
            self.show_message(f"邀请发送失败：{result}", "error")

    def on_batch_invitation_sent(self, success, result, emails):
        """批量邀请发送完成回调"""
        self.hide_loading()

        if success:
            self.show_message(f"批量邀请发送成功！共发送 {len(emails)} 个邀请", "success")
            self.batch_email_input.clear()
            self.update_batch_count()
            # 更新统计
            self.update_invitation_stats()
        else:
            self.show_message(f"批量邀请发送失败：{result}", "error")

    def refresh_team_members(self):
        """刷新团队成员列表"""
        self.show_loading("正在加载团队成员...")
        self.members_loading_label.setText("正在加载团队成员数据...")

        # 使用线程获取团队数据
        self.worker_thread = WorkerThread(
            target=self.api_client.get_team_data
        )
        self.worker_thread.finished.connect(self.on_team_data_loaded)
        self.worker_thread.start()

    def on_team_data_loaded(self, success, result):
        """团队数据加载完成回调"""
        self.hide_loading()

        if success:
            self.team_data = result
            self.update_team_members_table()
            self.update_team_stats()
            self.show_message("团队成员数据加载成功", "success")
        else:
            self.show_message(f"加载团队数据失败：{result}", "error")
            self.members_loading_label.setText(f"加载失败：{result}")

    def update_team_members_table(self):
        """更新团队成员表格"""
        if not self.team_data:
            return

        # 获取成员数据
        members = self.team_data.get('members', [])
        self.members_table.setRowCount(len(members))

        for row, member in enumerate(members):
            # 邮箱
            email_item = QTableWidgetItem(member.get('email', ''))
            self.members_table.setItem(row, 0, email_item)

            # 状态
            status = member.get('status', 'unknown')
            status_item = QTableWidgetItem(status)
            if status == 'active':
                status_item.setStyleSheet(f"color: {ModernStyleManager.SUCCESS};")
            elif status == 'pending':
                status_item.setStyleSheet(f"color: {ModernStyleManager.WARNING};")
            else:
                status_item.setStyleSheet(f"color: {ModernStyleManager.ERROR};")
            self.members_table.setItem(row, 1, status_item)

            # 加入时间
            join_time = member.get('joinedAt', '未知')
            self.members_table.setItem(row, 2, QTableWidgetItem(join_time))

            # 最后活跃
            last_active = member.get('lastActive', '未知')
            self.members_table.setItem(row, 3, QTableWidgetItem(last_active))

            # 操作按钮
            action_widget = QWidget()
            action_layout = QHBoxLayout(action_widget)
            action_layout.setContentsMargins(4, 4, 4, 4)
            action_layout.setSpacing(8)

            delete_btn = ModernStyleManager.create_modern_button("删除", "ghost", "🗑️", "small")
            delete_btn.clicked.connect(lambda checked, m=member: self.delete_member(m))
            action_layout.addWidget(delete_btn)

            self.members_table.setCellWidget(row, 4, action_widget)

        self.members_loading_label.hide()

    def update_team_stats(self):
        """更新团队统计"""
        if not self.team_data:
            return

        members = self.team_data.get('members', [])
        pending_invites = self.team_data.get('pendingInvites', [])

        # 更新统计卡片
        total_members = len(members)
        active_members = len([m for m in members if m.get('status') == 'active'])
        pending_count = len(pending_invites)

        self.team_members_stat.value_label.setText(str(total_members))
        self.pending_invites_stat.value_label.setText(str(pending_count))
        self.active_members_stat.value_label.setText(str(active_members))

        # 更新仪表板统计
        if hasattr(self, 'members_stat'):
            self.members_stat.value_label.setText(str(total_members))
        if hasattr(self, 'invites_stat'):
            self.invites_stat.value_label.setText(str(pending_count))

    def refresh_pending_invites(self):
        """刷新待处理邀请"""
        self.show_loading("正在加载待处理邀请...")
        self.pending_loading_label.setText("正在加载待处理邀请数据...")

        # 使用团队数据中的待处理邀请
        if self.team_data:
            self.update_pending_invites_table()
        else:
            # 如果没有团队数据，先获取团队数据
            self.refresh_team_members()

    def update_pending_invites_table(self):
        """更新待处理邀请表格"""
        if not self.team_data:
            return

        pending_invites = self.team_data.get('pendingInvites', [])
        self.pending_table.setRowCount(len(pending_invites))

        for row, invite in enumerate(pending_invites):
            # 邮箱
            email_item = QTableWidgetItem(invite.get('email', ''))
            self.pending_table.setItem(row, 0, email_item)

            # 邀请时间
            invite_time = invite.get('invitedAt', '未知')
            self.pending_table.setItem(row, 1, QTableWidgetItem(invite_time))

            # 状态
            status = invite.get('status', 'pending')
            status_item = QTableWidgetItem(status)
            status_item.setStyleSheet(f"color: {ModernStyleManager.WARNING};")
            self.pending_table.setItem(row, 2, status_item)

            # 操作按钮
            action_widget = QWidget()
            action_layout = QHBoxLayout(action_widget)
            action_layout.setContentsMargins(4, 4, 4, 4)
            action_layout.setSpacing(8)

            cancel_btn = ModernStyleManager.create_modern_button("取消", "ghost", "❌", "small")
            cancel_btn.clicked.connect(lambda checked, inv=invite: self.cancel_invite(inv))
            action_layout.addWidget(cancel_btn)

            resend_btn = ModernStyleManager.create_modern_button("重发", "ghost", "🔄", "small")
            resend_btn.clicked.connect(lambda checked, inv=invite: self.resend_invite(inv))
            action_layout.addWidget(resend_btn)

            self.pending_table.setCellWidget(row, 3, action_widget)

        self.pending_loading_label.hide()
        self.hide_loading()

    def delete_member(self, member):
        """删除团队成员"""
        email = member.get('email', '')
        member_id = member.get('id', '')

        if not member_id:
            self.show_message("无法删除成员：缺少成员ID", "error")
            return

        self.show_loading(f"正在删除成员 {email}...")

        # 使用线程删除成员
        self.worker_thread = WorkerThread(
            target=self.api_client.delete_member,
            args=(member_id,)
        )
        self.worker_thread.finished.connect(lambda success, result: self.on_member_deleted(success, result, email))
        self.worker_thread.start()

    def on_member_deleted(self, success, result, email):
        """成员删除完成回调"""
        self.hide_loading()

        if success:
            self.show_message(f"成员 {email} 删除成功", "success")
            # 刷新成员列表
            self.refresh_team_members()
        else:
            self.show_message(f"删除成员失败：{result}", "error")

    def cancel_invite(self, invite):
        """取消邀请"""
        email = invite.get('email', '')
        invite_id = invite.get('id', '')

        if not invite_id:
            self.show_message("无法取消邀请：缺少邀请ID", "error")
            return

        self.show_loading(f"正在取消邀请 {email}...")

        # 使用线程取消邀请
        self.worker_thread = WorkerThread(
            target=self.api_client.delete_member,
            args=(invite_id,)
        )
        self.worker_thread.finished.connect(lambda success, result: self.on_invite_cancelled(success, result, email))
        self.worker_thread.start()

    def on_invite_cancelled(self, success, result, email):
        """邀请取消完成回调"""
        self.hide_loading()

        if success:
            self.show_message(f"邀请 {email} 取消成功", "success")
            # 刷新邀请列表
            self.refresh_pending_invites()
        else:
            self.show_message(f"取消邀请失败：{result}", "error")

    def resend_invite(self, invite):
        """重新发送邀请"""
        email = invite.get('email', '')

        self.show_loading(f"正在重新发送邀请到 {email}...")

        # 使用线程重新发送邀请
        self.worker_thread = WorkerThread(
            target=self.api_client.invite_members,
            args=([email],)
        )
        self.worker_thread.finished.connect(lambda success, result: self.on_invite_resent(success, result, email))
        self.worker_thread.start()

    def on_invite_resent(self, success, result, email):
        """邀请重发完成回调"""
        self.hide_loading()

        if success:
            self.show_message(f"邀请已重新发送到 {email}", "success")
        else:
            self.show_message(f"重发邀请失败：{result}", "error")

    def export_team_members(self):
        """导出团队成员"""
        if not self.team_data:
            self.show_message("请先加载团队数据", "warning")
            return

        members = self.team_data.get('members', [])
        if not members:
            self.show_message("没有团队成员数据可导出", "warning")
            return

        # 生成导出内容
        export_lines = []
        for member in members:
            email = member.get('email', '')
            status = member.get('status', '')
            join_time = member.get('joinedAt', '')
            export_lines.append(f"{email}\t{status}\t{join_time}")

        export_content = "邮箱\t状态\t加入时间\n" + "\n".join(export_lines)

        # 这里应该保存到文件，暂时显示在消息中
        self.show_message(f"导出完成，共 {len(members)} 个成员", "success")
        print("导出的团队成员数据：")
        print(export_content)

    def export_pending_emails(self):
        """导出待处理邀请邮箱"""
        if not self.team_data:
            self.show_message("请先加载团队数据", "warning")
            return

        pending_invites = self.team_data.get('pendingInvites', [])
        if not pending_invites:
            self.show_message("没有待处理邀请可导出", "warning")
            return

        # 生成邮箱列表
        emails = [invite.get('email', '') for invite in pending_invites if invite.get('email')]
        export_content = "\n".join(emails)

        self.show_message(f"导出完成，共 {len(emails)} 个邮箱", "success")
        print("导出的待处理邀请邮箱：")
        print(export_content)

    def bulk_delete_members(self):
        """批量删除成员"""
        self.show_message("批量删除功能开发中...", "info")

    def cancel_all_invites(self):
        """取消所有邀请"""
        self.show_message("取消所有邀请功能开发中...", "info")

    # 批量操作页面功能方法
    def switch_to_community_plan(self):
        """切换到 Community Plan"""
        self.show_loading("正在切换到 Community Plan...")

        self.worker_thread = WorkerThread(
            target=self.api_client.put_user_on_community_plan
        )
        self.worker_thread.finished.connect(lambda success, result: self.on_plan_switched(success, result, "Community Plan"))
        self.worker_thread.start()

    def switch_to_max_plan(self):
        """切换到 Max Plan"""
        self.show_loading("正在切换到 Max Plan...")

        self.worker_thread = WorkerThread(
            target=self.api_client.put_user_on_max_plan
        )
        self.worker_thread.finished.connect(lambda success, result: self.on_plan_switched(success, result, "Max Plan"))
        self.worker_thread.start()

    def on_plan_switched(self, success, result, plan_name):
        """计划切换完成回调"""
        self.hide_loading()

        if success:
            self.show_message(f"成功切换到 {plan_name}", "success")
            # 更新统计
            if hasattr(self, 'plan_switches_stat'):
                current_count = int(self.plan_switches_stat.value_label.text())
                self.plan_switches_stat.value_label.setText(str(current_count + 1))
        else:
            self.show_message(f"切换到 {plan_name} 失败：{result}", "error")

    def select_email_file(self):
        """选择邮箱文件"""
        from PyQt6.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择邮箱文件",
            "",
            "文本文件 (*.txt);;CSV文件 (*.csv);;所有文件 (*)"
        )

        if file_path:
            self.selected_file_path = file_path
            self.show_message(f"已选择文件：{file_path}", "info")
        else:
            self.show_message("未选择文件", "warning")

    def import_emails_from_file(self):
        """从文件导入邮箱"""
        if not hasattr(self, 'selected_file_path'):
            self.show_message("请先选择文件", "warning")
            return

        try:
            with open(self.selected_file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析邮箱地址
            import re
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            emails = re.findall(email_pattern, content)

            if not emails:
                self.show_message("文件中未找到有效的邮箱地址", "warning")
                return

            # 验证邮箱
            valid_emails = [email for email in emails if self.api_client.validate_email(email)]

            if not valid_emails:
                self.show_message("文件中没有有效的邮箱地址", "warning")
                return

            # 将邮箱导入到批量邀请输入框
            if hasattr(self, 'batch_email_input'):
                current_text = self.batch_email_input.toPlainText().strip()
                if current_text:
                    new_text = current_text + '\n' + '\n'.join(valid_emails)
                else:
                    new_text = '\n'.join(valid_emails)

                self.batch_email_input.setPlainText(new_text)
                self.update_batch_count()

                # 切换到邀请成员页面
                self.switch_page(1)

                self.show_message(f"成功导入 {len(valid_emails)} 个邮箱地址", "success")
            else:
                self.show_message(f"找到 {len(valid_emails)} 个有效邮箱，请手动复制", "info")
                print("导入的邮箱地址：")
                for email in valid_emails:
                    print(email)

        except Exception as e:
            self.show_message(f"读取文件失败：{str(e)}", "error")

    def export_all_members(self):
        """导出所有成员数据"""
        if not self.team_data:
            self.show_message("请先在团队管理页面加载数据", "warning")
            return

        members = self.team_data.get('members', [])
        pending_invites = self.team_data.get('pendingInvites', [])

        if not members and not pending_invites:
            self.show_message("没有数据可导出", "warning")
            return

        # 生成完整的导出数据
        export_lines = ["类型\t邮箱\t状态\t时间"]

        for member in members:
            email = member.get('email', '')
            status = member.get('status', '')
            join_time = member.get('joinedAt', '')
            export_lines.append(f"成员\t{email}\t{status}\t{join_time}")

        for invite in pending_invites:
            email = invite.get('email', '')
            status = invite.get('status', 'pending')
            invite_time = invite.get('invitedAt', '')
            export_lines.append(f"邀请\t{email}\t{status}\t{invite_time}")

        export_content = "\n".join(export_lines)

        # 更新统计
        if hasattr(self, 'exports_stat'):
            current_count = int(self.exports_stat.value_label.text())
            self.exports_stat.value_label.setText(str(current_count + 1))

        self.show_message(f"导出完成，共 {len(members) + len(pending_invites)} 条记录", "success")
        print("导出的完整团队数据：")
        print(export_content)

    def cleanup_invalid_invites(self):
        """清理无效邀请"""
        self.show_message("清理无效邀请功能开发中...", "info")

    # 数据视图页面功能方法
    def refresh_data_analysis(self):
        """刷新数据分析"""
        self.show_loading("正在分析数据...")
        self.data_loading_label.setText("正在分析团队数据...")

        # 如果没有团队数据，先获取
        if not self.team_data:
            self.worker_thread = WorkerThread(
                target=self.api_client.get_team_data
            )
            self.worker_thread.finished.connect(self.on_data_analysis_loaded)
            self.worker_thread.start()
        else:
            # 直接分析现有数据
            self.analyze_team_data()

    def on_data_analysis_loaded(self, success, result):
        """数据分析加载完成回调"""
        if success:
            self.team_data = result
            self.analyze_team_data()
        else:
            self.hide_loading()
            self.show_message(f"数据加载失败：{result}", "error")
            self.data_loading_label.setText(f"数据加载失败：{result}")

    def analyze_team_data(self):
        """分析团队数据"""
        if not self.team_data:
            return

        members = self.team_data.get('members', [])
        pending_invites = self.team_data.get('pendingInvites', [])

        # 计算各种指标
        total_members = len(members)
        active_members = len([m for m in members if m.get('status') == 'active'])
        pending_count = len(pending_invites)

        # 计算比率
        active_rate = (active_members / total_members * 100) if total_members > 0 else 0
        invite_success_rate = (active_members / (active_members + pending_count) * 100) if (active_members + pending_count) > 0 else 0

        # 更新关键指标
        self.total_members_metric.value_label.setText(str(total_members))
        self.growth_rate_metric.value_label.setText("5.2%")  # 模拟数据
        self.active_rate_metric.value_label.setText(f"{active_rate:.1f}%")
        self.invite_success_metric.value_label.setText(f"{invite_success_rate:.1f}%")

        # 更新状态分布
        inactive_members = total_members - active_members
        self.active_members_count.value_label.setText(str(active_members))
        self.pending_members_count.value_label.setText(str(pending_count))
        self.inactive_members_count.value_label.setText(str(inactive_members))

        # 更新邀请趋势（模拟数据）
        self.today_invites_count.value_label.setText("3")
        self.week_invites_count.value_label.setText("15")
        self.month_invites_count.value_label.setText("42")

        # 更新数据摘要表格
        self.update_data_summary_table()

        self.hide_loading()
        self.data_loading_label.hide()
        self.show_message("数据分析完成", "success")

    def update_data_summary_table(self):
        """更新数据摘要表格"""
        if not self.team_data:
            return

        members = self.team_data.get('members', [])
        pending_invites = self.team_data.get('pendingInvites', [])

        # 准备表格数据
        summary_data = [
            ("总成员数", str(len(members)), "包括所有状态的成员"),
            ("活跃成员", str(len([m for m in members if m.get('status') == 'active'])), "当前活跃的成员"),
            ("待处理邀请", str(len(pending_invites)), "等待接受的邀请"),
            ("成员增长", "+5.2%", "相比上月的增长率"),
            ("邀请成功率", "78.5%", "邀请被接受的比例"),
            ("平均响应时间", "2.3天", "邀请到接受的平均时间"),
            ("最活跃时段", "工作日下午", "邀请接受的高峰时段"),
            ("地区分布", "多元化", "成员的地理分布情况")
        ]

        self.data_summary_table.setRowCount(len(summary_data))

        for row, (metric, value, description) in enumerate(summary_data):
            self.data_summary_table.setItem(row, 0, QTableWidgetItem(metric))

            value_item = QTableWidgetItem(value)
            if "+" in value or "%" in value:
                value_item.setStyleSheet(f"color: {ModernStyleManager.SUCCESS}; font-weight: 600;")
            self.data_summary_table.setItem(row, 1, value_item)

            self.data_summary_table.setItem(row, 2, QTableWidgetItem(description))

    def export_data_report(self):
        """导出数据报告"""
        if not self.team_data:
            self.show_message("请先刷新数据", "warning")
            return

        members = self.team_data.get('members', [])
        pending_invites = self.team_data.get('pendingInvites', [])

        # 生成报告内容
        report_lines = [
            "=== 团队管理数据报告 ===",
            f"生成时间：{QTimer().singleShot.__name__}",  # 简化的时间戳
            "",
            "=== 基础统计 ===",
            f"总成员数：{len(members)}",
            f"活跃成员：{len([m for m in members if m.get('status') == 'active'])}",
            f"待处理邀请：{len(pending_invites)}",
            "",
            "=== 成员列表 ===",
        ]

        for member in members:
            email = member.get('email', '')
            status = member.get('status', '')
            join_time = member.get('joinedAt', '')
            report_lines.append(f"{email}\t{status}\t{join_time}")

        report_lines.append("")
        report_lines.append("=== 待处理邀请 ===")

        for invite in pending_invites:
            email = invite.get('email', '')
            invite_time = invite.get('invitedAt', '')
            report_lines.append(f"{email}\t{invite_time}")

        report_content = "\n".join(report_lines)

        self.show_message(f"报告导出完成，共 {len(report_lines)} 行", "success")
        print("导出的数据报告：")
        print(report_content)

    def perform_deep_analysis(self):
        """执行深度分析"""
        if not self.team_data:
            self.show_message("请先刷新数据", "warning")
            return

        self.show_loading("正在执行深度分析...")

        # 模拟深度分析过程
        QTimer.singleShot(2000, self.on_deep_analysis_complete)

    def on_deep_analysis_complete(self):
        """深度分析完成"""
        self.hide_loading()

        analysis_results = [
            "📊 深度分析结果：",
            "• 团队活跃度：良好",
            "• 邀请转化率：高于平均水平",
            "• 成员参与度：稳定增长",
            "• 建议：继续保持当前邀请策略"
        ]

        result_text = "\n".join(analysis_results)
        self.show_message("深度分析完成", "success")
        print(result_text)

    # 设置页面功能方法
    def set_theme(self, theme):
        """设置主题"""
        ModernStyleManager.switch_theme(theme)

        # 重新应用样式
        app = QApplication.instance()
        app.setStyleSheet(ModernStyleManager.get_app_style())

        theme_name = "深色" if theme == "dark" else "浅色"
        self.show_message(f"已切换到{theme_name}主题", "success")

    def reset_config(self):
        """重置配置"""
        self.show_message("配置重置功能开发中...", "info")

    def clear_cache(self):
        """清除缓存"""
        self.show_message("缓存已清除", "success")

    def export_all_data(self):
        """导出所有数据"""
        if not self.team_data:
            self.show_message("没有数据可导出，请先在其他页面加载数据", "warning")
            return

        self.show_loading("正在导出所有数据...")

        # 模拟导出过程
        QTimer.singleShot(1500, self.on_export_complete)

    def on_export_complete(self):
        """导出完成"""
        self.hide_loading()
        self.show_message("所有数据导出完成", "success")

    def backup_settings(self):
        """备份设置"""
        self.show_message("设置备份完成", "success")

    def check_for_updates(self):
        """检查更新"""
        self.show_loading("正在检查更新...")

        # 模拟检查更新
        QTimer.singleShot(2000, self.on_update_check_complete)

    def on_update_check_complete(self):
        """更新检查完成"""
        self.hide_loading()
        self.show_message("当前已是最新版本", "success")

    def show_help(self):
        """显示帮助"""
        help_text = """
🛠️ 团队管理工具 - 帮助文档

📊 仪表板：查看团队概览和关键统计
👥 邀请成员：发送单个或批量邀请
🔧 团队管理：管理现有成员和待处理邀请
⚡ 批量操作：执行批量操作和计划管理
📈 数据视图：查看详细的数据分析
⚙️ 设置：配置应用和管理数据

如需更多帮助，请查看完整文档。
        """
        self.show_message("帮助信息已显示", "info")
        print(help_text)

    def update_refresh_timer(self):
        """更新刷新定时器"""
        # 兼容性方法
        pass

    def auto_refresh_data(self):
        """自动刷新数据"""
        # 兼容性方法
        pass

    def show_message(self, message, msg_type="info"):
        """显示消息提示"""
        if hasattr(self, 'top_status_label'):
            self.top_status_label.setText(message)

        # 根据消息类型设置颜色
        colors = {
            "success": ModernStyleManager.SUCCESS,
            "error": ModernStyleManager.ERROR,
            "warning": ModernStyleManager.WARNING,
            "info": ModernStyleManager.INFO
        }

        color = colors.get(msg_type, ModernStyleManager.TEXT_SECONDARY)
        if hasattr(self, 'top_status_label'):
            self.top_status_label.setStyleSheet(f"color: {color}; font-size: 14px; font-weight: 500;")

        print(f"[{msg_type.upper()}] {message}")

    def show_loading(self, message="加载中..."):
        """显示加载状态"""
        self.show_message(f"⏳ {message}", "info")

    def hide_loading(self):
        """隐藏加载状态"""
        self.show_message("就绪", "info")

    def update_invitation_stats(self):
        """更新邀请统计"""
        # 这里应该从实际数据更新统计信息
        # 暂时使用模拟数据
        if hasattr(self, 'today_invites_mini'):
            current_count = int(self.today_invites_mini.value_label.text())
            self.today_invites_mini.value_label.setText(str(current_count + 1))

        if hasattr(self, 'total_invites_mini'):
            current_count = int(self.total_invites_mini.value_label.text())
            self.total_invites_mini.value_label.setText(str(current_count + 1))

    def log_info(self, category, message):
        """记录日志信息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {category}: {message}"
        self.log_entries.append(log_entry)
        print(log_entry)  # 临时输出到控制台
