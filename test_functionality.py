#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
功能测试脚本
"""

import sys
import traceback

def test_imports():
    """测试模块导入"""
    try:
        print("测试模块导入...")
        
        from config import Config
        print("✅ Config模块导入成功")
        
        from api import APIClient
        print("✅ APIClient模块导入成功")
        
        from ui import StyleManager, TeamManagerMainWindow
        print("✅ UI模块导入成功")
        
        from utils import WorkerThread
        print("✅ WorkerThread模块导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        traceback.print_exc()
        return False

def test_object_creation():
    """测试对象创建"""
    try:
        print("\n测试对象创建...")
        
        from config import Config
        config = Config()
        print("✅ 配置对象创建成功")
        
        from api import APIClient
        api_client = APIClient(config)
        print("✅ API客户端创建成功")
        
        return True
    except Exception as e:
        print(f"❌ 对象创建失败: {e}")
        traceback.print_exc()
        return False

def test_ui_creation():
    """测试UI创建"""
    try:
        print("\n测试UI创建...")
        
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QTimer
        
        app = QApplication([])
        print("✅ QApplication创建成功")
        
        from ui import TeamManagerMainWindow
        window = TeamManagerMainWindow()
        print("✅ 主窗口创建成功")
        
        # 快速退出
        QTimer.singleShot(100, app.quit)
        app.exec()
        
        return True
    except Exception as e:
        print(f"❌ UI创建失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 开始功能测试...\n")
    
    tests = [
        test_imports,
        test_object_creation,
        test_ui_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            break
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！功能正常工作。")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
