# -*- coding: utf-8 -*-
"""
现代化UI样式系统 - 全新设计
支持深浅主题、动画效果、现代化组件
"""

from PyQt6.QtWidgets import (
    QPushButton, QFrame, QVBoxLayout, QHBoxLayout, QLabel, 
    QGraphicsDropShadowEffect, QGraphicsBlurEffect, QScrollArea,
    QWidget, QApplication
)
from PyQt6.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect, pyqtProperty
from PyQt6.QtGui import QColor, QPalette, QFont


class ModernStyleManager:
    """超美观现代化样式管理器 - 顶级设计版本"""

    # 🌈 顶级渐变色彩系统 - 灵感来自顶级设计
    # 主渐变 - 深邃蓝紫
    PRIMARY_GRADIENT_START = "#667eea"
    PRIMARY_GRADIENT_END = "#764ba2"
    PRIMARY = "#667eea"
    PRIMARY_LIGHT = "#8b9cf7"
    PRIMARY_DARK = "#5a67d8"

    # 辅助渐变 - 温暖粉橙
    SECONDARY_GRADIENT_START = "#f093fb"
    SECONDARY_GRADIENT_END = "#f5576c"
    SECONDARY = "#f093fb"
    SECONDARY_LIGHT = "#f5a3ff"
    SECONDARY_DARK = "#e879f9"

    # 成功渐变 - 清新青绿
    SUCCESS_GRADIENT_START = "#4facfe"
    SUCCESS_GRADIENT_END = "#00f2fe"
    SUCCESS = "#4facfe"
    SUCCESS_LIGHT = "#7cc7ff"
    SUCCESS_DARK = "#2563eb"

    # 警告渐变 - 活力橙黄
    WARNING_GRADIENT_START = "#ffecd2"
    WARNING_GRADIENT_END = "#fcb69f"
    WARNING = "#f59e0b"
    WARNING_LIGHT = "#fbbf24"
    WARNING_DARK = "#d97706"

    # 错误渐变 - 优雅红粉
    ERROR_GRADIENT_START = "#ff9a9e"
    ERROR_GRADIENT_END = "#fecfef"
    ERROR = "#ef4444"
    ERROR_LIGHT = "#f87171"
    ERROR_DARK = "#dc2626"

    # 信息渐变 - 清澈蓝青
    INFO_GRADIENT_START = "#a8edea"
    INFO_GRADIENT_END = "#fed6e3"
    INFO = "#06b6d4"
    INFO_LIGHT = "#22d3ee"
    INFO_DARK = "#0891b2"

    # 🎨 精致的中性色系
    # 背景色系
    BG_MAIN = "#fafbfc"           # 主背景 - 极浅灰
    BG_SECONDARY = "#f7f9fc"      # 次背景 - 浅灰蓝
    BG_TERTIARY = "#eef2f7"       # 三级背景 - 中浅灰

    # 表面色系
    SURFACE_PRIMARY = "#ffffff"    # 主表面 - 纯白
    SURFACE_SECONDARY = "#fdfdfe"  # 次表面 - 微灰白
    SURFACE_ELEVATED = "#ffffff"   # 悬浮表面 - 纯白

    # 边框色系
    BORDER_LIGHT = "#e8ecf0"      # 浅边框
    BORDER_MEDIUM = "#d1d9e0"     # 中边框
    BORDER_STRONG = "#b8c5d1"     # 强边框

    # 文字色系
    TEXT_PRIMARY = "#1a202c"      # 主文字 - 深灰
    TEXT_SECONDARY = "#4a5568"    # 次文字 - 中灰
    TEXT_TERTIARY = "#718096"     # 三级文字 - 浅灰
    TEXT_QUATERNARY = "#a0aec0"   # 四级文字 - 极浅灰

    # 🌙 深色主题色系
    DARK_BG_MAIN = "#0d1117"      # 主背景 - 深黑
    DARK_BG_SECONDARY = "#161b22" # 次背景 - 深灰
    DARK_BG_TERTIARY = "#21262d"  # 三级背景 - 中深灰

    DARK_SURFACE_PRIMARY = "#161b22"    # 主表面
    DARK_SURFACE_SECONDARY = "#21262d"  # 次表面
    DARK_SURFACE_ELEVATED = "#2d333b"   # 悬浮表面

    DARK_BORDER_LIGHT = "#30363d"       # 浅边框
    DARK_BORDER_MEDIUM = "#373e47"      # 中边框
    DARK_BORDER_STRONG = "#444c56"      # 强边框

    DARK_TEXT_PRIMARY = "#f0f6fc"       # 主文字
    DARK_TEXT_SECONDARY = "#c9d1d9"     # 次文字
    DARK_TEXT_TERTIARY = "#8b949e"      # 三级文字
    DARK_TEXT_QUATERNARY = "#6e7681"    # 四级文字

    # 当前主题
    current_theme = "light"
    
    @classmethod
    def get_color(cls, color_name):
        """根据当前主题获取颜色"""
        theme_prefix = cls.current_theme.upper()
        color_key = f"{theme_prefix}_{color_name.upper()}"
        return getattr(cls, color_key, color_name)
    
    @classmethod
    def switch_theme(cls, theme="light"):
        """切换主题"""
        cls.current_theme = theme
        
    @staticmethod
    def get_app_style():
        """获取超美观的应用程序样式"""
        return f"""
        /* 🎨 全局样式 - 顶级设计 */
        QMainWindow {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {ModernStyleManager.BG_MAIN},
                stop:0.3 {ModernStyleManager.BG_SECONDARY},
                stop:1 {ModernStyleManager.BG_TERTIARY});
            color: {ModernStyleManager.TEXT_PRIMARY};
            font-family: 'Microsoft YaHei UI', 'SF Pro Display', 'Segoe UI', sans-serif;
            font-size: 14px;
            font-weight: 400;
        }}

        QWidget {{
            background: transparent;
            color: {ModernStyleManager.TEXT_PRIMARY};
        }}

        /* 🔄 超美观滚动条 */
        QScrollArea {{
            border: none;
            background: transparent;
        }}

        QScrollBar:vertical {{
            background: rgba(255, 255, 255, 0.1);
            width: 6px;
            border-radius: 3px;
            margin: 0;
        }}

        QScrollBar::handle:vertical {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {ModernStyleManager.PRIMARY_GRADIENT_START},
                stop:1 {ModernStyleManager.PRIMARY_GRADIENT_END});
            border-radius: 3px;
            min-height: 30px;
        }}

        QScrollBar::handle:vertical:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {ModernStyleManager.PRIMARY_LIGHT},
                stop:1 {ModernStyleManager.PRIMARY_DARK});
        }}

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}

        QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
            background: transparent;
        }}

        /* 📝 精美输入框 */
        QLineEdit, QTextEdit {{
            background: {ModernStyleManager.SURFACE_PRIMARY};
            border: 2px solid {ModernStyleManager.BORDER_LIGHT};
            border-radius: 12px;
            padding: 16px 20px;
            font-size: 15px;
            font-weight: 400;
            color: {ModernStyleManager.TEXT_PRIMARY};
            selection-background-color: {ModernStyleManager.PRIMARY}30;
        }}

        QLineEdit:focus, QTextEdit:focus {{
            border: 2px solid {ModernStyleManager.PRIMARY};
            background: {ModernStyleManager.SURFACE_ELEVATED};
        }}

        QLineEdit:hover, QTextEdit:hover {{
            border-color: {ModernStyleManager.BORDER_MEDIUM};
        }}

        /* 📊 优雅表格 */
        QTableWidget {{
            background: {ModernStyleManager.SURFACE_PRIMARY};
            border: 1px solid {ModernStyleManager.BORDER_LIGHT};
            border-radius: 16px;
            gridline-color: {ModernStyleManager.BORDER_LIGHT};
            selection-background-color: {ModernStyleManager.PRIMARY}15;
            font-size: 14px;
        }}

        QTableWidget::item {{
            padding: 16px;
            border-bottom: 1px solid {ModernStyleManager.BORDER_LIGHT};
        }}

        QTableWidget::item:selected {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {ModernStyleManager.PRIMARY}20,
                stop:1 {ModernStyleManager.PRIMARY}10);
            color: {ModernStyleManager.PRIMARY_DARK};
        }}

        QHeaderView::section {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {ModernStyleManager.PRIMARY_GRADIENT_START},
                stop:1 {ModernStyleManager.PRIMARY_GRADIENT_END});
            color: white;
            padding: 16px;
            border: none;
            font-weight: 600;
            font-size: 14px;
            border-radius: 8px;
        }}

        /* 🎛️ 精致控件 */
        QComboBox {{
            background: {ModernStyleManager.SURFACE_PRIMARY};
            border: 2px solid {ModernStyleManager.BORDER_LIGHT};
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 14px;
            min-width: 140px;
        }}

        QComboBox:focus {{
            border-color: {ModernStyleManager.PRIMARY};
        }}

        QComboBox:hover {{
            border-color: {ModernStyleManager.BORDER_MEDIUM};
        }}

        QCheckBox {{
            spacing: 12px;
            font-size: 14px;
            font-weight: 500;
        }}

        QCheckBox::indicator {{
            width: 20px;
            height: 20px;
            border: 2px solid {ModernStyleManager.BORDER_MEDIUM};
            border-radius: 6px;
            background: {ModernStyleManager.SURFACE_PRIMARY};
        }}

        QCheckBox::indicator:checked {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {ModernStyleManager.PRIMARY_GRADIENT_START},
                stop:1 {ModernStyleManager.PRIMARY_GRADIENT_END});
            border-color: {ModernStyleManager.PRIMARY};
        }}

        QCheckBox::indicator:hover {{
            border-color: {ModernStyleManager.PRIMARY};
        }}
        """

    @staticmethod
    def apply_glassmorphism(widget, blur_radius=20, opacity=0.8):
        """应用毛玻璃效果"""
        blur_effect = QGraphicsBlurEffect()
        blur_effect.setBlurRadius(blur_radius)
        widget.setGraphicsEffect(blur_effect)
        widget.setWindowOpacity(opacity)

    @staticmethod
    def apply_shadow(widget, blur_radius=15, offset=(0, 4), color=None):
        """应用阴影效果"""
        if color is None:
            color = QColor(0, 0, 0, 30)
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(blur_radius)
        shadow.setXOffset(offset[0])
        shadow.setYOffset(offset[1])
        shadow.setColor(color)
        widget.setGraphicsEffect(shadow)

    @staticmethod
    def create_modern_button(text, style="primary", icon=None, size="medium"):
        """创建超美观现代化按钮"""
        button = QPushButton(text)

        # 按钮尺寸
        sizes = {
            "small": ("10px 20px", "13px", "32px"),
            "medium": ("16px 32px", "15px", "44px"),
            "large": ("20px 40px", "17px", "52px")
        }
        padding, font_size, min_height = sizes.get(size, sizes["medium"])

        if icon:
            button.setText(f"{icon}  {text}")

        # 按钮样式定义
        if style == "primary":
            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {ModernStyleManager.PRIMARY_GRADIENT_START},
                        stop:1 {ModernStyleManager.PRIMARY_GRADIENT_END});
                    color: white;
                    border: none;
                    border-radius: 12px;
                    padding: {padding};
                    font-size: {font_size};
                    font-weight: 600;
                    min-height: {min_height};
                }}

                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {ModernStyleManager.PRIMARY_LIGHT},
                        stop:1 {ModernStyleManager.PRIMARY_DARK});
                }}

                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {ModernStyleManager.PRIMARY_DARK},
                        stop:1 {ModernStyleManager.PRIMARY});
                }}
            """)
        elif style == "secondary":
            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {ModernStyleManager.SECONDARY_GRADIENT_START},
                        stop:1 {ModernStyleManager.SECONDARY_GRADIENT_END});
                    color: white;
                    border: none;
                    border-radius: 12px;
                    padding: {padding};
                    font-size: {font_size};
                    font-weight: 600;
                    min-height: {min_height};
                }}

                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {ModernStyleManager.SECONDARY_LIGHT},
                        stop:1 {ModernStyleManager.SECONDARY_DARK});
                }}
            """)
        elif style == "success":
            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {ModernStyleManager.SUCCESS_GRADIENT_START},
                        stop:1 {ModernStyleManager.SUCCESS_GRADIENT_END});
                    color: white;
                    border: none;
                    border-radius: 12px;
                    padding: {padding};
                    font-size: {font_size};
                    font-weight: 600;
                    min-height: {min_height};
                }}
            """)
        elif style == "outline":
            button.setStyleSheet(f"""
                QPushButton {{
                    background: {ModernStyleManager.SURFACE_PRIMARY};
                    color: {ModernStyleManager.PRIMARY};
                    border: 2px solid {ModernStyleManager.PRIMARY};
                    border-radius: 12px;
                    padding: {padding};
                    font-size: {font_size};
                    font-weight: 600;
                    min-height: {min_height};
                }}

                QPushButton:hover {{
                    background: {ModernStyleManager.PRIMARY}10;
                    border-color: {ModernStyleManager.PRIMARY_DARK};
                }}
            """)
        elif style == "ghost":
            button.setStyleSheet(f"""
                QPushButton {{
                    background: transparent;
                    color: {ModernStyleManager.PRIMARY};
                    border: none;
                    border-radius: 12px;
                    padding: {padding};
                    font-size: {font_size};
                    font-weight: 600;
                    min-height: {min_height};
                }}

                QPushButton:hover {{
                    background: {ModernStyleManager.PRIMARY}15;
                }}
            """)

        # 添加精致阴影效果
        ModernStyleManager.apply_shadow(button, blur_radius=12, offset=(0, 4))
        return button

    @staticmethod
    def create_card(title="", content_widget=None, icon=None, style="default"):
        """创建超美观现代化卡片"""
        card = QFrame()
        card.setFrameShape(QFrame.Shape.NoFrame)

        # 精美卡片样式
        if style == "default":
            background = f"""
                background: {ModernStyleManager.SURFACE_PRIMARY};
                border: 1px solid {ModernStyleManager.BORDER_LIGHT};
            """
        elif style == "primary":
            background = f"""
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {ModernStyleManager.PRIMARY}08,
                    stop:0.5 {ModernStyleManager.SURFACE_PRIMARY},
                    stop:1 {ModernStyleManager.PRIMARY}05);
                border: 1px solid {ModernStyleManager.PRIMARY}20;
            """
        elif style == "success":
            background = f"""
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {ModernStyleManager.SUCCESS}08,
                    stop:0.5 {ModernStyleManager.SURFACE_PRIMARY},
                    stop:1 {ModernStyleManager.SUCCESS}05);
                border: 1px solid {ModernStyleManager.SUCCESS}20;
            """
        elif style == "gradient":
            background = f"""
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {ModernStyleManager.PRIMARY_GRADIENT_START}15,
                    stop:0.5 {ModernStyleManager.SURFACE_PRIMARY},
                    stop:1 {ModernStyleManager.SECONDARY_GRADIENT_START}15);
                border: 1px solid {ModernStyleManager.PRIMARY}30;
            """
        else:
            background = f"""
                background: {ModernStyleManager.SURFACE_PRIMARY};
                border: 1px solid {ModernStyleManager.BORDER_LIGHT};
            """

        card.setStyleSheet(f"""
            QFrame {{
                {background}
                border-radius: 20px;
                padding: 0px;
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(32, 28, 32, 32)
        layout.setSpacing(20)

        # 添加精美标题
        if title:
            header_layout = QHBoxLayout()
            header_layout.setContentsMargins(0, 0, 0, 0)
            header_layout.setSpacing(16)

            if icon:
                icon_container = QFrame()
                icon_container.setFixedSize(48, 48)
                icon_container.setStyleSheet(f"""
                    QFrame {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 {ModernStyleManager.PRIMARY_GRADIENT_START}20,
                            stop:1 {ModernStyleManager.PRIMARY_GRADIENT_END}20);
                        border: 2px solid {ModernStyleManager.PRIMARY}30;
                        border-radius: 24px;
                    }}
                """)

                icon_layout = QVBoxLayout(icon_container)
                icon_layout.setContentsMargins(0, 0, 0, 0)

                icon_label = QLabel(icon)
                icon_label.setStyleSheet(f"""
                    font-size: 22px;
                    color: {ModernStyleManager.PRIMARY};
                    background: transparent;
                    border: none;
                """)
                icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                icon_layout.addWidget(icon_label)

                header_layout.addWidget(icon_container)

            title_label = QLabel(title)
            title_label.setStyleSheet(f"""
                font-size: 22px;
                font-weight: 700;
                color: {ModernStyleManager.TEXT_PRIMARY};
                margin: 0;
                letter-spacing: -0.5px;
            """)
            header_layout.addWidget(title_label)
            header_layout.addStretch()

            layout.addLayout(header_layout)

            # 添加分隔线
            separator = QFrame()
            separator.setFixedHeight(1)
            separator.setStyleSheet(f"""
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent,
                    stop:0.2 {ModernStyleManager.BORDER_LIGHT},
                    stop:0.8 {ModernStyleManager.BORDER_LIGHT},
                    stop:1 transparent);
                border: none;
            """)
            layout.addWidget(separator)

        # 添加内容
        if content_widget:
            layout.addWidget(content_widget)

        # 添加精致多层阴影效果
        ModernStyleManager.apply_shadow(card, blur_radius=20, offset=(0, 8))
        return card

    @staticmethod
    def create_stat_card(icon, title, value, subtitle="", color=None):
        """创建超美观统计卡片"""
        if color is None:
            color = ModernStyleManager.PRIMARY

        card = QFrame()
        card.setFixedHeight(140)

        # 根据颜色选择渐变
        if color == ModernStyleManager.PRIMARY:
            gradient_start = ModernStyleManager.PRIMARY_GRADIENT_START
            gradient_end = ModernStyleManager.PRIMARY_GRADIENT_END
        elif color == ModernStyleManager.SUCCESS:
            gradient_start = ModernStyleManager.SUCCESS_GRADIENT_START
            gradient_end = ModernStyleManager.SUCCESS_GRADIENT_END
        elif color == ModernStyleManager.WARNING:
            gradient_start = ModernStyleManager.WARNING_GRADIENT_START
            gradient_end = ModernStyleManager.WARNING_GRADIENT_END
        elif color == ModernStyleManager.INFO:
            gradient_start = ModernStyleManager.INFO_GRADIENT_START
            gradient_end = ModernStyleManager.INFO_GRADIENT_END
        else:
            gradient_start = color
            gradient_end = color

        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {gradient_start}12,
                    stop:0.5 {ModernStyleManager.SURFACE_PRIMARY},
                    stop:1 {gradient_end}08);
                border: 2px solid {color}25;
                border-radius: 20px;
            }}
        """)

        layout = QHBoxLayout(card)
        layout.setContentsMargins(24, 20, 24, 20)
        layout.setSpacing(20)

        # 精美图标区域
        icon_container = QFrame()
        icon_container.setFixedSize(70, 70)
        icon_container.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {gradient_start}25,
                    stop:1 {gradient_end}25);
                border: 3px solid {color}40;
                border-radius: 35px;
            }}
        """)

        icon_layout = QVBoxLayout(icon_container)
        icon_layout.setContentsMargins(0, 0, 0, 0)

        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            font-size: 28px;
            color: {color};
            background: transparent;
            border: none;
            font-weight: bold;
        """)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_layout.addWidget(icon_label)

        # 内容区域
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(0, 4, 0, 4)
        content_layout.setSpacing(6)

        # 大数值
        value_label = QLabel(str(value))
        value_label.setStyleSheet(f"""
            font-size: 32px;
            font-weight: 800;
            color: {color};
            margin: 0;
            letter-spacing: -1px;
        """)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 600;
            color: {ModernStyleManager.TEXT_PRIMARY};
            margin: 0;
        """)

        # 副标题
        if subtitle:
            subtitle_label = QLabel(subtitle)
            subtitle_label.setStyleSheet(f"""
                font-size: 13px;
                color: {ModernStyleManager.TEXT_SECONDARY};
                margin: 0;
                font-weight: 400;
            """)
            content_layout.addWidget(subtitle_label)

        content_layout.addWidget(value_label)
        content_layout.addWidget(title_label)
        content_layout.addStretch()

        layout.addWidget(icon_container)
        layout.addLayout(content_layout)
        layout.addStretch()

        # 添加精致阴影效果
        ModernStyleManager.apply_shadow(card, blur_radius=16, offset=(0, 6))

        # 存储值标签以便更新
        card.value_label = value_label
        return card

    @staticmethod
    def create_navigation_button(text, icon, is_active=False):
        """创建导航按钮"""
        button = QPushButton(f"{icon}  {text}")

        if is_active:
            button.setStyleSheet(f"""
                QPushButton {{
                    background: {ModernStyleManager.PRIMARY};
                    color: white;
                    border: none;
                    border-radius: 12px;
                    padding: 16px 24px;
                    font-size: 14px;
                    font-weight: 600;
                    text-align: left;
                    min-height: 20px;
                }}

                QPushButton:hover {{
                    background: {ModernStyleManager.PRIMARY_DARK};
                }}
            """)
        else:
            button.setStyleSheet(f"""
                QPushButton {{
                    background: transparent;
                    color: {ModernStyleManager.get_color('text_secondary')};
                    border: none;
                    border-radius: 12px;
                    padding: 16px 24px;
                    font-size: 14px;
                    font-weight: 500;
                    text-align: left;
                    min-height: 20px;
                }}

                QPushButton:hover {{
                    background: {ModernStyleManager.PRIMARY}10;
                    color: {ModernStyleManager.PRIMARY};
                }}
            """)

        return button
