# -*- coding: utf-8 -*-
"""
现代化UI样式系统 - 全新设计
支持深浅主题、动画效果、现代化组件
"""

from PyQt6.QtWidgets import (
    QPushButton, QFrame, QVBoxLayout, QHBoxLayout, QLabel, 
    QGraphicsDropShadowEffect, QGraphicsBlurEffect, QScrollArea,
    QWidget, QApplication
)
from PyQt6.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect, pyqtProperty
from PyQt6.QtGui import QColor, QPalette, QFont


class ModernStyleManager:
    """现代化样式管理器 - 2024版本"""
    
    # 🎨 现代化颜色系统
    # 主色调 - 现代紫色系
    PRIMARY = "#6366f1"           # 主紫色
    PRIMARY_LIGHT = "#8b5cf6"     # 浅紫色  
    PRIMARY_DARK = "#4f46e5"      # 深紫色
    PRIMARY_ALPHA = "#6366f120"   # 透明主色
    
    # 辅助色调
    SECONDARY = "#ec4899"         # 粉色
    SECONDARY_LIGHT = "#f472b6"   # 浅粉色
    SECONDARY_DARK = "#db2777"    # 深粉色
    
    # 功能色调
    SUCCESS = "#10b981"           # 成功绿
    SUCCESS_LIGHT = "#34d399"     # 浅绿
    SUCCESS_DARK = "#059669"      # 深绿
    
    WARNING = "#f59e0b"           # 警告橙
    WARNING_LIGHT = "#fbbf24"     # 浅橙
    WARNING_DARK = "#d97706"      # 深橙
    
    ERROR = "#ef4444"             # 错误红
    ERROR_LIGHT = "#f87171"       # 浅红
    ERROR_DARK = "#dc2626"        # 深红
    
    INFO = "#06b6d4"              # 信息青
    INFO_LIGHT = "#22d3ee"        # 浅青
    INFO_DARK = "#0891b2"         # 深青
    
    # 中性色调 - 浅色主题
    LIGHT_BG = "#ffffff"          # 主背景
    LIGHT_BG_SECONDARY = "#f8fafc" # 次背景
    LIGHT_BG_TERTIARY = "#f1f5f9"  # 三级背景
    LIGHT_SURFACE = "#ffffff"      # 表面色
    LIGHT_BORDER = "#e2e8f0"       # 边框色
    LIGHT_TEXT = "#0f172a"         # 主文字
    LIGHT_TEXT_SECONDARY = "#475569" # 次文字
    LIGHT_TEXT_TERTIARY = "#94a3b8"  # 三级文字
    
    # 中性色调 - 深色主题
    DARK_BG = "#0f172a"           # 主背景
    DARK_BG_SECONDARY = "#1e293b" # 次背景  
    DARK_BG_TERTIARY = "#334155"  # 三级背景
    DARK_SURFACE = "#1e293b"      # 表面色
    DARK_BORDER = "#475569"       # 边框色
    DARK_TEXT = "#f8fafc"         # 主文字
    DARK_TEXT_SECONDARY = "#cbd5e1" # 次文字
    DARK_TEXT_TERTIARY = "#94a3b8"  # 三级文字
    
    # 当前主题（默认浅色）
    current_theme = "light"
    
    @classmethod
    def get_color(cls, color_name):
        """根据当前主题获取颜色"""
        theme_prefix = cls.current_theme.upper()
        color_key = f"{theme_prefix}_{color_name.upper()}"
        return getattr(cls, color_key, color_name)
    
    @classmethod
    def switch_theme(cls, theme="light"):
        """切换主题"""
        cls.current_theme = theme
        
    @staticmethod
    def get_app_style():
        """获取应用程序全局样式"""
        return f"""
        /* 🎨 全局样式 */
        QMainWindow {{
            background: {ModernStyleManager.get_color('bg')};
            color: {ModernStyleManager.get_color('text')};
            font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
            font-size: 13px;
        }}
        
        QWidget {{
            background: transparent;
            color: {ModernStyleManager.get_color('text')};
        }}
        
        /* 🔄 滚动条样式 */
        QScrollArea {{
            border: none;
            background: transparent;
        }}
        
        QScrollBar:vertical {{
            background: {ModernStyleManager.get_color('bg_secondary')};
            width: 8px;
            border-radius: 4px;
            margin: 0;
        }}
        
        QScrollBar::handle:vertical {{
            background: {ModernStyleManager.PRIMARY}40;
            border-radius: 4px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background: {ModernStyleManager.PRIMARY}60;
        }}
        
        QScrollBar::handle:vertical:pressed {{
            background: {ModernStyleManager.PRIMARY}80;
        }}
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}
        
        QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
            background: transparent;
        }}
        
        /* 📝 输入框样式 */
        QLineEdit, QTextEdit {{
            background: {ModernStyleManager.get_color('surface')};
            border: 2px solid {ModernStyleManager.get_color('border')};
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            color: {ModernStyleManager.get_color('text')};
        }}
        
        QLineEdit:focus, QTextEdit:focus {{
            border-color: {ModernStyleManager.PRIMARY};
            background: {ModernStyleManager.get_color('surface')};
        }}
        
        /* 📊 表格样式 */
        QTableWidget {{
            background: {ModernStyleManager.get_color('surface')};
            border: 1px solid {ModernStyleManager.get_color('border')};
            border-radius: 12px;
            gridline-color: {ModernStyleManager.get_color('border')};
            selection-background-color: {ModernStyleManager.PRIMARY}20;
        }}
        
        QTableWidget::item {{
            padding: 12px;
            border-bottom: 1px solid {ModernStyleManager.get_color('border')};
        }}
        
        QHeaderView::section {{
            background: {ModernStyleManager.PRIMARY};
            color: white;
            padding: 12px;
            border: none;
            font-weight: 600;
            font-size: 13px;
        }}
        
        /* 🎛️ 其他控件 */
        QComboBox {{
            background: {ModernStyleManager.get_color('surface')};
            border: 2px solid {ModernStyleManager.get_color('border')};
            border-radius: 8px;
            padding: 8px 12px;
            min-width: 120px;
        }}
        
        QComboBox:focus {{
            border-color: {ModernStyleManager.PRIMARY};
        }}
        
        QCheckBox {{
            spacing: 8px;
            font-size: 14px;
        }}
        
        QCheckBox::indicator {{
            width: 18px;
            height: 18px;
            border: 2px solid {ModernStyleManager.get_color('border')};
            border-radius: 4px;
            background: {ModernStyleManager.get_color('surface')};
        }}
        
        QCheckBox::indicator:checked {{
            background: {ModernStyleManager.PRIMARY};
            border-color: {ModernStyleManager.PRIMARY};
        }}
        """

    @staticmethod
    def apply_glassmorphism(widget, blur_radius=20, opacity=0.8):
        """应用毛玻璃效果"""
        blur_effect = QGraphicsBlurEffect()
        blur_effect.setBlurRadius(blur_radius)
        widget.setGraphicsEffect(blur_effect)
        widget.setWindowOpacity(opacity)

    @staticmethod
    def apply_shadow(widget, blur_radius=15, offset=(0, 4), color=None):
        """应用阴影效果"""
        if color is None:
            color = QColor(0, 0, 0, 30)
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(blur_radius)
        shadow.setXOffset(offset[0])
        shadow.setYOffset(offset[1])
        shadow.setColor(color)
        widget.setGraphicsEffect(shadow)

    @staticmethod
    def create_modern_button(text, style="primary", icon=None, size="medium"):
        """创建现代化按钮"""
        button = QPushButton(text)

        # 按钮尺寸
        sizes = {
            "small": ("8px 16px", "12px", "28px"),
            "medium": ("12px 24px", "14px", "36px"),
            "large": ("16px 32px", "16px", "44px")
        }
        padding, font_size, min_height = sizes.get(size, sizes["medium"])

        # 按钮样式
        styles = {
            "primary": (ModernStyleManager.PRIMARY, "white"),
            "secondary": (ModernStyleManager.SECONDARY, "white"),
            "success": (ModernStyleManager.SUCCESS, "white"),
            "warning": (ModernStyleManager.WARNING, "white"),
            "error": (ModernStyleManager.ERROR, "white"),
            "info": (ModernStyleManager.INFO, "white"),
            "outline": (ModernStyleManager.get_color('border'), ModernStyleManager.PRIMARY),
            "ghost": ("transparent", ModernStyleManager.PRIMARY)
        }
        bg_color, text_color = styles.get(style, styles["primary"])

        if icon:
            button.setText(f"{icon} {text}")

        button.setStyleSheet(f"""
            QPushButton {{
                background: {bg_color};
                color: {text_color};
                border: 2px solid {bg_color if style != 'outline' else ModernStyleManager.PRIMARY};
                border-radius: 8px;
                padding: {padding};
                font-size: {font_size};
                font-weight: 600;
                min-height: {min_height};
                text-align: center;
            }}

            QPushButton:hover {{
                background: {ModernStyleManager.PRIMARY_DARK if style == 'primary' else bg_color + '80'};
            }}

            QPushButton:pressed {{
                background: {ModernStyleManager.PRIMARY_DARK if style == 'primary' else bg_color + '60'};
            }}

            QPushButton:disabled {{
                background: {ModernStyleManager.get_color('border')};
                color: {ModernStyleManager.get_color('text_tertiary')};
                border-color: {ModernStyleManager.get_color('border')};
            }}
        """)

        # 添加阴影效果
        ModernStyleManager.apply_shadow(button, blur_radius=8, offset=(0, 2))
        return button

    @staticmethod
    def create_card(title="", content_widget=None, icon=None, style="default"):
        """创建现代化卡片"""
        card = QFrame()
        card.setFrameShape(QFrame.Shape.NoFrame)

        # 卡片样式
        card_styles = {
            "default": ModernStyleManager.get_color('surface'),
            "primary": f"qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 {ModernStyleManager.PRIMARY}10, stop:1 {ModernStyleManager.get_color('surface')})",
            "success": f"qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 {ModernStyleManager.SUCCESS}10, stop:1 {ModernStyleManager.get_color('surface')})",
            "warning": f"qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 {ModernStyleManager.WARNING}10, stop:1 {ModernStyleManager.get_color('surface')})"
        }

        background = card_styles.get(style, card_styles["default"])

        card.setStyleSheet(f"""
            QFrame {{
                background: {background};
                border: 1px solid {ModernStyleManager.get_color('border')};
                border-radius: 16px;
                padding: 0px;
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(16)

        # 添加标题
        if title:
            header_layout = QHBoxLayout()
            header_layout.setContentsMargins(0, 0, 0, 0)
            header_layout.setSpacing(12)

            if icon:
                icon_label = QLabel(icon)
                icon_label.setStyleSheet(f"""
                    font-size: 20px;
                    color: {ModernStyleManager.PRIMARY};
                    background: {ModernStyleManager.PRIMARY}15;
                    border-radius: 8px;
                    padding: 8px;
                    min-width: 36px;
                    min-height: 36px;
                """)
                icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                header_layout.addWidget(icon_label)

            title_label = QLabel(title)
            title_label.setStyleSheet(f"""
                font-size: 18px;
                font-weight: 700;
                color: {ModernStyleManager.get_color('text')};
                margin: 0;
            """)
            header_layout.addWidget(title_label)
            header_layout.addStretch()

            layout.addLayout(header_layout)

        # 添加内容
        if content_widget:
            layout.addWidget(content_widget)

        # 添加阴影效果
        ModernStyleManager.apply_shadow(card, blur_radius=12, offset=(0, 4))
        return card

    @staticmethod
    def create_stat_card(icon, title, value, subtitle="", color=None):
        """创建统计卡片"""
        if color is None:
            color = ModernStyleManager.PRIMARY

        card = QFrame()
        card.setFixedHeight(120)
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color}15, stop:1 {ModernStyleManager.get_color('surface')});
                border: 1px solid {color}30;
                border-radius: 16px;
                border-left: 4px solid {color};
            }}
        """)

        layout = QHBoxLayout(card)
        layout.setContentsMargins(20, 16, 20, 16)
        layout.setSpacing(16)

        # 图标区域
        icon_container = QFrame()
        icon_container.setFixedSize(60, 60)
        icon_container.setStyleSheet(f"""
            QFrame {{
                background: {color}20;
                border-radius: 30px;
                border: 2px solid {color}40;
            }}
        """)

        icon_layout = QVBoxLayout(icon_container)
        icon_layout.setContentsMargins(0, 0, 0, 0)

        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            font-size: 24px;
            color: {color};
            background: transparent;
            border: none;
        """)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_layout.addWidget(icon_label)

        # 内容区域
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(4)

        # 数值
        value_label = QLabel(str(value))
        value_label.setStyleSheet(f"""
            font-size: 28px;
            font-weight: 800;
            color: {color};
            margin: 0;
        """)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            font-size: 14px;
            font-weight: 600;
            color: {ModernStyleManager.get_color('text')};
            margin: 0;
        """)

        # 副标题
        if subtitle:
            subtitle_label = QLabel(subtitle)
            subtitle_label.setStyleSheet(f"""
                font-size: 12px;
                color: {ModernStyleManager.get_color('text_secondary')};
                margin: 0;
            """)
            content_layout.addWidget(subtitle_label)

        content_layout.addWidget(value_label)
        content_layout.addWidget(title_label)
        content_layout.addStretch()

        layout.addWidget(icon_container)
        layout.addLayout(content_layout)
        layout.addStretch()

        # 添加阴影效果
        ModernStyleManager.apply_shadow(card, blur_radius=8, offset=(0, 2))

        # 存储值标签以便更新
        card.value_label = value_label
        return card

    @staticmethod
    def create_navigation_button(text, icon, is_active=False):
        """创建导航按钮"""
        button = QPushButton(f"{icon}  {text}")

        if is_active:
            button.setStyleSheet(f"""
                QPushButton {{
                    background: {ModernStyleManager.PRIMARY};
                    color: white;
                    border: none;
                    border-radius: 12px;
                    padding: 16px 24px;
                    font-size: 14px;
                    font-weight: 600;
                    text-align: left;
                    min-height: 20px;
                }}

                QPushButton:hover {{
                    background: {ModernStyleManager.PRIMARY_DARK};
                }}
            """)
        else:
            button.setStyleSheet(f"""
                QPushButton {{
                    background: transparent;
                    color: {ModernStyleManager.get_color('text_secondary')};
                    border: none;
                    border-radius: 12px;
                    padding: 16px 24px;
                    font-size: 14px;
                    font-weight: 500;
                    text-align: left;
                    min-height: 20px;
                }}

                QPushButton:hover {{
                    background: {ModernStyleManager.PRIMARY}10;
                    color: {ModernStyleManager.PRIMARY};
                }}
            """)

        return button
